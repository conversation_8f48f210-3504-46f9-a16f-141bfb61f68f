<?php
return [
   
        'validation_error' => 'Erreur de validation',
        'token_generated_success' => 'Jeton généré avec succès.',
        'channel_name_required' => 'Le champ Nom du canal est requis.',
        'to_user_id_required' => 'L ID de l utilisateur destinataire est requis.',
        'user_is_live' => 'L utilisateur est en direct',
        'live_stream_ended' => 'Diffusion en direct terminée',
        'is_live_notification' => 'est en direct',
        'is_calling_you' => 'vous appelle',
        'type_required' => 'Le champ Type est requis.',
        'call_history_fetch_success' => 'Historique des appels récupéré avec succès.',
        'history_not_found' => 'Historique introuvable.',
        'stream_not_found' => 'Diffusion introuvable.',
        'live_stream_join_success' => 'Diffusion en direct rejointe avec succès.',
        'already_joined_stream' => 'Vous avez déjà rejoint cette diffusion.',
        'create_course_success' => 'Le cours a été créé avec succès.',
        'token_fetch_success' => 'Jeton récupéré avec succès.',
        'notification_sent_success' => 'Notification envoyée avec succès.',
        'call_declined' => 'a refusé votre appel',
        'user_id_required' => 'Le champ ID de l utilisateur est requis.',
        'livestream_request_accepted' => 'a accepté la demande de diffusion en direct',
        'live_stream_users_fetch_success' => 'Utilisateurs de la diffusion en direct récupérés avec succès.',
        'no_live_stream_users_found' => 'Aucun utilisateur de la diffusion en direct trouvé.',
        'user_already_in_livestream' => 'L utilisateur est déjà dans la diffusion en direct.',
        'user_added_to_livestream' => 'Utilisateur ajouté à la diffusion en direct avec succès.',
        'user_not_in_livestream' => 'L utilisateur n est pas dans la diffusion en direct.',
        'user_removed_from_livestream' => 'Utilisateur retiré de la diffusion en direct avec succès.',
        'invalid_choice' => 'Choix invalide.',
        'live_stream_user_found_success' => 'Utilisateurs de la diffusion en direct trouvés avec succès.',
        'user_fetch_success' => 'Utilisateurs récupérés avec succès.',
        'user_not_found' => 'Utilisateur non trouvé.',
        'cannot_send_gift_to_self' => 'Vous ne pouvez pas vous envoyer un cadeau à vous-même.',
        'gift_not_found' => 'Cadeau non trouvé.',
        'insufficient_balance' => 'Solde insuffisant.',
        'gift_sent_success' => 'Cadeau envoyé avec succès.',
        'gift_id_required' => 'Le champ ID du cadeau est requis.',
        'call_not_found' => 'Appel introuvable.',
        'unauthenticated' => 'Non authentifié.',
        'call_deleted_success' => 'Appel supprimé avec succès.',
        'call_id_required' => 'Le champ ID de l appel est requis.',
        'course_updated_successfully' => 'Le cours a été mis à jour avec succès.',
        'course_deleted_successfully' => 'Le cours a été supprimé avec succès.',
        'call_history_deleted_success' => 'Historique des appels supprimé avec succès.',
        'email_required' => 'Le champ Email est requis.',
        'email_invalid' => 'Veuillez fournir une adresse e-mail valide.',
        'password_required' => 'Le champ Mot de passe est requis.',
        'password_min_length' => 'Le mot de passe doit contenir au moins 6 caractères.',
        'email_password_mismatch' => 'L e-mail ou le mot de passe ne correspondent pas.',
        'account_verification' => 'Veuillez vérifier votre e-mail pour le lien de vérification. Vérifiez également votre dossier spam.',
        'login_success' => 'Connexion réussie.',
        'email_unique' => 'Cette adresse e-mail est déjà enregistrée. Veuillez utiliser une autre adresse e-mail.',
        'password_confirm_mismatch' => 'Les mots de passe ne correspondent pas.',
        'date_of_birth_required' => 'Le champ Date de naissance est requis.',
        'gender_required' => 'Le champ Sexe est requis.',
        'registration_success' => 'Inscription réussie. Veuillez vous connecter.',
        'demo_restriction' => 'Vous ne pouvez pas effectuer cette action en mode démo.',
        'file_upload_success' => 'Données mises à jour avec succès.',
        'site_logo_upload_error' => 'Une erreur est survenue lors du téléchargement du logo du site.',
        'favicon_upload_error' => 'Une erreur est survenue lors du téléchargement du favicon.',
        'noFriendRequests' => 'Aucune demande d ami trouvée',
        'block_self_error' => 'Vous ne pouvez pas vous bloquer vous-même.',
        'user_unblocked_success' => 'Utilisateur débloqué avec succès.',
        'user_blocked_success' => 'Utilisateur bloqué avec succès.',
        'block_user_fetch_success' => 'Utilisateur bloqué récupéré avec succès.',
        'block_user_not_found' => 'Utilisateur bloqué non trouvé.',
        'user_already_reported' => 'Vous avez déjà signalé cet utilisateur.',
        'user_reported_success' => 'Utilisateur signalé avec succès.',
        'report_user_id_required' => 'Le champ ID de l utilisateur signalé est requis.',
        'reason_required' => 'Le champ Raison est requis.',
        'blog_fetch_success' => 'Blogs récupérés avec succès.',
        'no_blog_found' => 'Aucun blog trouvé.',
        'tags_fetch_success' => 'Étiquettes récupérées avec succès.',
        'blood_request_not_found' => 'Demande de sang non trouvée.',
        'unauthenticated_error' => 'Vous n êtes pas autorisé à supprimer cette demande de sang.',
        'blood_request_delete_success' => 'Demande de sang supprimée avec succès.',
        'messages_fetch_success' => 'Messages récupérés avec succès.',
        'no_messages_found' => 'Aucun message trouvé.',
        'error_occurred' => 'Une erreur est survenue : {0}',
        'to_id_required' => 'L ID du destinataire est requis.',
        'to_id_integer' => 'L ID du destinataire doit être un entier valide.',
        'chat_list_fetch_success' => 'Liste des discussions récupérée avec succès.',
        'no_chats_found' => 'Aucune discussion trouvée pour la page donnée.',
        'page_id_required' => 'Le champ ID de la page est requis.',
        'page_id_integer' => 'L ID de la page doit être un entier valide.',
        'message_deleted_success' => 'Le message a été supprimé avec succès.',
        'not_allowed_to_delete_message' => 'Vous n êtes pas autorisé à supprimer ce message.',
        'message_not_found' => 'Message non trouvé.',
        'chat_users_fetched_success' => 'Utilisateurs du chat récupérés avec succès.',
        'paypal_deposit_success' => 'Montant déposé avec succès en utilisant PayPal.',
        'stripe_deposit_success' => 'Montant déposé avec succès en utilisant Stripe.',
        'paystack_deposit_success' => 'Montant déposé avec succès en utilisant Paystack.',
        'flutterwave_deposit_success' => 'Montant déposé avec succès en utilisant Flutterwave.',
        'gateway_id_required' => 'La passerelle de paiement est requise.',
        'transaction_id_required' => 'L ID de la transaction est requis.',
        'event_created_success' => 'Événement créé avec succès.',
        'name_required' => 'Le nom de l événement est requis.',
        'name_invalid' => 'Le nom de l événement ne peut contenir que des caractères alphanumériques et des espaces.',
        'location_required' => 'L emplacement de l événement est requis.',
        'start_date_required' => 'La date de début est requise.',
        'start_time_required' => 'L heure de début est requise.',
        'end_date_required' => 'La date de fin est requise.',
        'end_time_required' => 'L heure de fin est requise.',
        'fetch_events_success' => 'Événements récupérés avec succès.',
        'events_not_found' => 'Aucun événement trouvé.',
        'fetch_interested_success' => 'Événements intéressés récupérés avec succès.',
        'no_interest_events_found' => 'Aucun événement intéressé trouvé.',
        'fetch_going_success' => 'Événements en cours récupérés avec succès.',
        'going_events_not_found' => 'Aucun événement en cours trouvé.',
        'fetch_my_events_success' => 'Mes événements récupérés avec succès.',
        'my_events_not_found' => 'Aucun événement trouvé pour vous.',
        'event_id_required' => 'L ID de l événement est requis.',
        'interest_marked_as_not_interested' => 'Événement marqué comme non intéressé.',
        'interest_marked_as_interested' => 'Événement marqué comme intéressé.',
        'going_marked_as_not_going' => 'Événement marqué comme ne pas y aller.',
        'going_marked_as_going' => 'Événement marqué comme y allant.',
        'event_update_success' => 'Champs de l événement mis à jour avec succès.',
        'event_update_failure' => 'Échec de la mise à jour de l événement.',
        'not_allowed' => 'Vous n êtes pas autorisé à effectuer cette action.',
        'event_not_found' => 'Événement non trouvé.',
        'event_deleted_success' => 'Événement supprimé avec succès.',
        'request_action_required' => 'L action de la demande est requise.',
        'friend_request_accepted' => 'Demande d ami acceptée.',
        'friend_request_declined' => 'Demande d ami refusée.',
        'friend_request_not_found' => 'Demande d ami non trouvée.',
        'userNotFound' => 'Utilisateur non trouvé',
        'privacySettings' => 'Impossible de procéder à la demande en raison des paramètres de confidentialité.',
        'requestCancelled' => 'Demande d ami annulée.',
        'alreadyFriends' => 'Vous êtes déjà amis.',
        'pendingRequest' => 'Vous avez une demande d ami en attente de cet utilisateur.',
        'requestSent' => 'Demande d ami envoyée avec succès.',
        'validationError' => 'Erreur de validation',
        'apiKeyUnauthorized' => 'Non autorisé',
        'friend_two_required' => 'Le champ friend_two est requis.',
        'request_id_required' => 'Le champ request_id est requis.',
        'requestSuccessfullyDeleted' => 'Demande d ami supprimée avec succès.',
        'requestNotFound' => 'Demande d ami non trouvée.',
        'unfriendSuccess' => 'Ami retiré avec succès.',
        'friendRequestNotFound' => 'Demande d ami non trouvée.',
        'noRecommendations' => 'Aucune recommandation trouvée.',
        'recommendationsFound' => 'Recommandations trouvées.',
        'role_updated_success' => 'Rôle de l ami mis à jour',
        'friend_list_fetch' => 'Liste des amis récupérée avec succès',
        'friend_not_found' => 'Vous n avez actuellement pas d amis.',
        'sent_requests_fetched' => 'Demandes envoyées récupérées avec succès.',
        'no_sent_requests' => 'Vous n avez aucune demande envoyée.',
        'fetch_games_success' => 'Données des jeux récupérées avec succès.',
        'no_games_found' => 'Aucun jeu trouvé.',
        'groups_fetched_successfully' => 'Groupes récupérés avec succès.',
        'no_groups_found' => 'Aucun groupe trouvé.',
        'user_groups_fetched_successfully' => 'Groupes d utilisateurs récupérés avec succès.',
        'no_user_groups_found' => 'Aucun groupe d utilisateurs trouvé.',
        'group_title_required' => 'Le titre du groupe est requis.',
        'group_title_invalid_characters' => 'Le titre du groupe contient des caractères invalides.',
        'about_group_required' => 'À propos du groupe est requis.',
        'category_required' => 'La catégorie est requise.',
        'privacy_required' => 'Le paramètre de confidentialité est requis.',
        'group_created_successfully' => 'Groupe créé avec succès.',
        'group_id_required' => 'L ID du groupe est requis.',
        'group_data_fetch_success' => 'Données du groupe récupérées avec succès.',
        'group_data_not_found' => 'Données du groupe non trouvées.',
        'group_update_success' => 'Champs du groupe mis à jour avec succès.',
        'unauthorized_access' => 'Vous n êtes pas autorisé à effectuer cette action.',
        'group_not_found' => 'Groupe non trouvé.',
        'already_member' => 'Vous êtes déjà membre de ce groupe.',
        'group_join_success' => 'Groupe rejoint avec succès.',
        'not_member' => 'Vous n êtes pas membre du groupe.',
        'group_left_success' => 'Groupe quitté avec succès.',
        'no_data_found' => 'Aucune donnée trouvée.',
        'member_already_in_group' => 'Ce membre est déjà dans le groupe.',
        'member_added_successfully' => 'Membre ajouté avec succès.',
        'user_not_member' => 'Cet utilisateur n est pas membre du groupe.',
        'member_removed_successfully' => 'Membre retiré avec succès.',
        'group_members_fetched_successfully' => 'Membres du groupe récupérés avec succès.',
        'group_members_not_found' => 'Membres du groupe non trouvés.',
        'admin_creation_success' => 'Admin du groupe créé avec succès.',
        'not_group_admin' => 'Cet utilisateur n est pas administrateur du groupe.',
        'admin_dismiss_success' => 'Administrateur du groupe retiré avec succès.',
        'groups_fetch_success' => 'Groupes récupérés avec succès.',
        'user_groups_not_found' => 'Groupes d utilisateurs non trouvés.',
        'job_id_required' => 'L ID de l emploi est requis.',
        'job_title_required' => 'Le titre de l emploi est requis.',
        'job_description_required' => 'La description de l emploi est requise.',
        'job_location_required' => 'L emplacement de l emploi est requis.',
        'minimum_salary_required' => 'Le salaire minimum est requis.',
        'maximum_salary_required' => 'Le salaire maximum est requis.',
        'currency_required' => 'La devise est requise.',
        'salary_date_required' => 'La date du salaire est requise.',
        'experience_years_required' => 'Les années d expérience sont requises.',
        'my_jobs_fetched_successfully' => 'Mes emplois récupérés avec succès.',
        'all_jobs_fetched_successfully' => 'Tous les emplois récupérés avec succès.',
        'no_job_found' => 'Aucun emploi trouvé.',
        'application_successful' => 'Candidature soumise avec succès.',
        'already_applied' => 'Vous avez déjà postulé pour cet emploi.',
        'job_id_integer' => 'L ID de l emploi doit être un entier.',
        'phone_required' => 'Le numéro de téléphone est requis.',
        'phone_invalid' => 'Le numéro de téléphone est invalide.',
        'cv_file_optional' => 'Le fichier CV est facultatif.',
        'cv_file_uploaded' => 'Le fichier CV doit être téléchargé.',
        'cv_file_max_size' => 'La taille du fichier CV ne doit pas dépasser 2 Mo.',
        'cv_file_mime_in' => 'Le fichier CV doit être un document PDF ou Word.',
        'applied_successfully' => 'Candidature réussie.',
        'already_applied_for_job' => 'Vous avez déjà postulé pour cet emploi.',
        'search_parameters_missing' => 'Veuillez saisir le type ou le titre de l emploi.',
        'search_success' => 'Emplois trouvés avec succès.',
        'no_jobs_found' => 'Aucun emploi trouvé.',
        'job_update_success' => 'Champs de l emploi mis à jour avec succès.',
        'unauthorized' => 'Vous n êtes pas autorisé.',
        'job_not_found' => 'Emploi non trouvé.',
        'job_categories_fetch_success' => 'Catégories d emploi récupérées avec succès.',
        'no_job_category_found' => 'Aucune catégorie d emploi trouvée.',
        'fetch_applied_candidates_success' => 'Candidats postulés récupérés avec succès.',
        'candidates_not_found' => 'Candidats non trouvés.',
        'fetch_notifications_success' => 'Notifications récupérées avec succès.',
        'no_notifications_found' => 'Aucune notification trouvée.',
        'notifications_list_success' => 'Liste des notifications récupérée avec succès.',
        'all_notifications_marked_as_read' => 'Toutes les notifications ont été marquées comme lues.',
        'all_notifications_deleted_successfully' => 'Toutes les notifications ont été supprimées avec succès.',
        'notification_not_found' => 'Notification non trouvée.',
        'notification_deleted_successfully' => 'Notification supprimée avec succès.',
        'notification_updated_successfully' => 'Notification mise à jour avec succès.',
        'job_deleted_successfully' => 'L emploi a été supprimé avec succès.',
        'transaction_failed' => 'Transaction échouée.',
        'page_created_successfully' => 'La page a été créée avec succès.',
        'page_title_required' => 'Le titre de la page est requis.',
        'page_title_min_length' => 'Le titre de la page doit contenir au moins 5 caractères.',
        'page_title_max_length' => 'Le titre de la page ne peut pas dépasser 50 caractères.',
        'page_title_invalid_characters' => 'Le titre de la page contient des caractères invalides.',
        'page_description_required' => 'La description de la page est requise.',
        'page_category_required' => 'La catégorie de la page est requise.',
        'page_deleted_successfully' => 'La page a été supprimée avec succès.',
        'page_not_found' => 'Page non trouvée.',
        'permission_denied' => 'Vous n êtes pas autorisé à mettre à jour cette page.',
        'page_updated_successfully' => 'Champs de la page mis à jour avec succès.',
        'email_subject' => 'A aimé votre page',
        'email_body_liked_page' => 'Quelqu un a aimé votre page.',
        'notification_liked_page' => 'a aimé votre page.',
        'push_notification_liked_page' => 'a aimé votre page',
        'page_successfully_liked' => 'Page aimée avec succès.',
        'page_successfully_unliked' => 'Page non aimée avec succès.',
        'fetch_liked_pages_success' => 'Pages aimées récupérées avec succès.',
        'no_liked_pages_found' => 'Aucune page aimée trouvée.',
        'deleted_user_id_required' => 'L ID de l utilisateur supprimé est requis.',
        'pages_fetch_success' => 'Pages récupérées avec succès.',
        'user_removed' => 'Utilisateur supprimé avec succès.',
        'post_text_required' => 'Le texte de la publication est requis.',
        'input_required' => 'Au moins l un des éléments suivants est requis : texte de la publication, images, audio ou vidéo.',
        'not_a_group_member' => 'Vous n êtes pas membre du groupe.',
        'post_created_success' => 'Publication créée avec succès.',
        'post_detail' => 'Détail de la publication',
        'post_saved_list' => 'Liste des publications enregistrées',
        'success' => 'Succès',
        'post_id' => 'ID de la publication',
        'post_id_required' => 'Le champ ID de la publication est requis.',
        'validation_failed' => 'Échec de la validation.',
        'post_not_found' => 'La publication n existe pas.',
        'post_detail_fetched' => 'Détail de la publication récupéré avec succès.',
        'post_deleted_successfully' => 'Publication supprimée avec succès.',
        'unauthorized_to_delete_post' => 'Vous n êtes pas autorisé à supprimer cette publication.',
        'post_id_numeric' => 'L ID de la publication doit être numérique.',
        'ad_title' => 'Titre de l annonce',
        'title_required' => 'Le titre est requis.',
        'title_max_length' => 'Le titre ne peut pas dépasser 150 caractères.',
        'ad_link' => 'Lien de l annonce',
        'link_required' => 'Le lien est requis.',
        'link_max_length' => 'Le lien ne peut pas dépasser 200 caractères.',
        'ad_body' => 'Corps de l annonce',
        'body_required' => 'Le corps est requis.',
        'body_max_length' => 'Le corps ne peut pas dépasser 250 caractères.',
        'image_upload_failed' => 'Échec du téléchargement de l image.',
        'advertisement_added_successfully' => 'Annonce ajoutée avec succès.',
        'failed_to_add_advertisement' => 'Échec de l ajout de l annonce.',
        'comment_text' => 'Texte du commentaire',
        'comment_text_required' => 'Le texte du commentaire est requis.',
        'comment_added_successfully' => 'Commentaire ajouté avec succès.',
        'failed_to_add_comment' => 'Échec de l ajout du commentaire.',
        'commented_on_post' => 'a commenté votre publication.',
        'post_comment' => 'Commenter la publication',
        'comments_fetched' => 'Commentaires récupérés avec succès.',
        'comments_not_found' => 'Aucun commentaire trouvé pour cette publication.',
        'comment_id' => 'ID du commentaire',
        'comment_id_required' => 'L ID du commentaire est requis.',
        'comment_id_numeric' => 'L ID du commentaire doit être numérique.',
        'comment_liked' => 'Commentaire aimé avec succès.',
        'comment_unliked' => 'Vous avez annulé votre "J aime" pour le commentaire.',
        'like_failed' => 'Échec de l aime du commentaire.',
        'new_comment_text' => 'Nouveau texte du commentaire',
        'new_comment_text_required' => 'Le texte du commentaire est requis.',
        'new_comment_text_string' => 'Le texte du commentaire doit être une chaîne valide.',
        'comment_not_found' => 'Commentaire non trouvé.',
        'comment_update_permission_denied' => 'Vous n êtes pas autorisé à mettre à jour ce commentaire.',
        'comment_updated_success' => 'Commentaire mis à jour avec succès.',
        'comment_update_failed' => 'Échec de la mise à jour du commentaire.',
        'reply_text' => 'Texte de la réponse',
        'reply_text_required' => 'Le texte de la réponse est requis.',
        'reply_text_string' => 'Le texte de la réponse doit être une chaîne valide.',
        'reply_added_successfully' => 'Réponse ajoutée avec succès.',
        'comment_reply_id' => 'ID de la réponse au commentaire',
        'comment_reply_id_required' => 'L ID de la réponse au commentaire est requis.',
        'comment_reply_id_numeric' => 'L ID de la réponse au commentaire doit être numérique.',
        'already_liked_comment_reply' => 'Vous avez déjà aimé cette réponse au commentaire.',
        'comment_reply_liked_successfully' => 'Réponse au commentaire aimée avec succès.',
        'comment_reply_like_failed' => 'Échec de l aime de la réponse au commentaire.',
        'reply_failed' => 'Échec de l ajout de la réponse au commentaire.',
        'post_shared_success' => 'La publication a été partagée avec succès.',
        'shared_your_post' => 'a partagé votre publication',
        'share_post_subject' => 'Partager la publication',
        'server_error' => 'Une erreur interne du serveur est survenue',
        'comment_deleted_success' => 'Commentaire supprimé avec succès.',
        'post_saved_success' => 'Publication enregistrée avec succès.',
        'saved_post_deleted_success' => 'Publication enregistrée supprimée avec succès.',
        'action' => 'Action',
        'action_required' => 'L action est requise.',
        'post_deleted_success' => 'Publication supprimée avec succès.',
        'unauthorized_delete' => 'Vous n êtes pas autorisé à supprimer cette publication.',
        'invalid_action' => 'Action invalide.',
        'post_reported_success' => 'Publication signalée avec succès.',
        'post_already_reported' => 'Vous avez déjà signalé cette publication.',
        'comments_disabled_success' => 'Commentaires désactivés avec succès.',
        'comments_enabled_success' => 'Commentaires activés avec succès.',
        'unauthorized_action' => 'Vous n êtes pas autorisé à effectuer cette action.',
        'reaction_removed_success' => 'Réaction supprimée avec succès.',
        'reaction_updated_success' => 'Réaction mise à jour avec succès.',
        'post_reaction_added_success' => 'Réaction ajoutée à la publication avec succès.',
        'reacted_on_your_post' => 'a réagi à votre publication.',
        'post_reaction_not_found' => 'Réaction à la publication non trouvée',
        'shared_post_deleted' => 'La publication partagée a été supprimée.',
        'reply_id_required' => 'L ID de la réponse est requis.',
        'reply_deleted_success' => 'La réponse au commentaire a été supprimée avec succès.',
        'comment_required' => 'Le texte du commentaire est requis.',
        'comment_reply_created_success' => 'Réponse au commentaire créée avec succès.',
        'comment_replies_success' => 'Réponses aux commentaires récupérées avec succès.',
        'great_job_already_assigned' => 'Great Job déjà attribué.',
        'own_post_great_job' => 'Ceci est votre propre publication.',
        'insufficient_balance_great_job' => 'Solde insuffisant pour attribuer un Great Job.',
        'great_job_awarded_success' => 'Great Job attribué avec succès.',
        'insufficient_balance_coc' => 'Solde insuffisant pour attribuer une Cup of Coffee.',
        'coc_already_assigned' => 'Cup of Coffee déjà attribuée.',
        'cannot_award_own_post_coc' => 'Vous ne pouvez pas attribuer une Cup of Coffee à votre propre publication.',
        'cup_of_coffee_awarded_success' => 'Cup of Coffee attribuée avec succès.',
        'error' => 'Erreur',
        'ad_not_found' => 'Annonce non trouvée',
        'ad_approved' => 'Votre demande d annonce a été approuvée',
        'ad_approve_success' => 'Demande d annonce approuvée avec succès',
        'ad_not_approved_balance' => 'Votre demande d annonce n a pas été approuvée en raison d un solde insuffisant.',
        'ad_approve_fail_balance' => 'La demande d annonce ne peut pas être approuvée en raison d un solde insuffisant',
        'ad_rejected' => 'Votre demande d annonce est rejetée',
        'ad_reject_success' => 'Demande d annonce rejetée avec succès',
        'ad_id_required' => 'L ID de l annonce est requis',
        'privacy_changed' => 'Confidentialité de la publication modifiée à {privacy}',
        'privacy_public' => 'Public',
        'privacy_friends' => 'Amis',
        'privacy_only_me' => 'Seulement moi',
        'privacy_family' => 'Famille',
        'privacy_business' => 'Entreprise',
        'post_updated' => 'La publication est mise à jour',
        'advertisement_request_fetch_success' => 'Demande d annonce récupérée avec succès',
        'advertisement_request_not_found' => 'Demande d annonce non trouvée',
        'status_pending' => 'En attente',
        'status_approved' => 'Approuvé',
        'status_rejected' => 'Rejeté',
        'poll_id_required' => 'L ID du sondage est requis',
        'poll_id_integer' => 'L ID du sondage doit être un entier',
        'poll_option_id_required' => 'L ID de l option du sondage est requis',
        'poll_option_id_integer' => 'L ID de l option du sondage doit être un entier',
        'poll_not_found' => 'Sondage non trouvé',
        'poll_option_not_found' => 'Option du sondage non trouvée',
        'already_voted' => 'Vous avez déjà voté, vous ne pouvez pas voter à nouveau',
        'vote_successful' => 'Vote réussi',
        'trending_hashtags_found' => 'Hashtags tendance trouvés avec succès',
        'trending_hashtags_not_exist' => 'Les hashtags tendance n existent pas',
        'amount_required' => 'Le montant est requis',
        'cannot_feed_own_post' => 'Vous ne pouvez pas nourrir votre propre publication',
        'product_name_required' => 'Le nom du produit est requis',
        'product_description_required' => 'La description du produit est requise',
        'price_required' => 'Le prix est requis',
        'units_required' => 'Les unités sont requises',
        'images_required' => 'Les images du produit sont requises',
        'images_ext_in' => 'Les images doivent être de type : png, jpg, jpeg',
        'images_is_image' => 'Le fichier doit être une image valide',
        'product_added_successfully' => 'Produit ajouté avec succès',
        'internal_server_error' => 'Erreur interne du serveur',
        'validation_errors' => 'Erreurs de validation',
        'post_feded_successfully' => 'Publication nourrie avec succès',
        'product_not_found' => 'Produit non trouvé',
        'fetch_user_product_success' => 'Produit utilisateur récupéré avec succès',
        'invalid_user_id' => 'ID utilisateur invalide',
        'product_id_required' => 'L ID du produit est requis',
        'product_updated_successfully' => 'Champs du produit mis à jour avec succès',
        'product_deleted_successfully' => 'Le produit est supprimé',
        'privacy_integer' => 'Le paramètre de confidentialité doit être un entier',
        'description_required' => 'La description est requise',
        'space_created_successfully' => 'L espace est créé avec succès',
        'space_id_required' => 'L ID de l espace est requis',
        'space_updated_successfully' => 'Champs de l espace mis à jour avec succès',
        'space_not_found' => 'Espace non trouvé',
        'space_deleted_successfully' => 'Espace supprimé avec succès',
        'cannot_join_own_space' => 'Vous ne pouvez pas rejoindre votre propre espace',
        'already_member_of_space' => 'Vous êtes déjà membre de cet espace',
        'space_joined_successfully' => 'Espace rejoint avec succès',
        'not_member_of_space' => 'Vous n êtes pas membre de cet espace',
        'space_left_successfully' => 'Espace quitté avec succès',
        'already_cohost' => 'Vous êtes déjà co-hôte de cet espace',
        'cohost_created_successfully' => 'Co-hôte créé avec succès',
        'user_not_member_of_space' => 'L utilisateur n est pas membre de cet espace',
        'cohost_removed_successfully' => 'Co-hôte supprimé avec succès',
        'not_a_cohost' => 'L utilisateur n est pas un co-hôte de cet espace',
        'spaces_fetched_successfully' => 'Espaces récupérés avec succès',
        'spaces_data_fetched_successfully' => 'Données des espaces récupérées avec succès',
        'story_created_successfully' => 'L histoire est créée avec succès',
        'stories_fetched_successfully' => 'Les histoires sont récupérées avec succès',
        'user_muted_successfully' => 'L utilisateur a été mis en sourdine avec succès',
        'user_unmuted_successfully' => 'L utilisateur a été désactivé avec succès',
        'story_id_required' => 'Le champ ID de l histoire est requis.',
        'own_story' => 'Ceci est votre propre histoire',
        'story_seen_successfully' => 'L histoire a été vue avec succès',
        'story_already_seen' => 'L histoire a déjà été vue',
        'viewed_story_notification' => 'a vu votre histoire.',
        'viewed_story_email_subject' => 'Histoire vue',
        'viewed_story_email_body' => 'a vu votre histoire',
        'story_seen_user_fetch_successfully' => 'Utilisateur ayant vu l histoire récupéré avec succès',
        'no_views_found' => 'Aucune vue trouvée',
        'story_deleted_successfully' => 'L histoire est supprimée avec succès',
        'story_not_found' => 'Histoire non trouvée',
        'blocked_user' => 'Utilisateur bloqué',
        'user_profile_fetch_successfully' => 'Profil utilisateur récupéré avec succès',
        'profile_not_found' => 'Profil non trouvé',
        'viewed_your_profile' => 'a vu votre profil',
        'view_profile_subject' => 'Voir le profil',
        'view_profile_text' => 'a vu votre profil',
        'search_user_fetch_successfully' => 'Utilisateur de recherche récupéré avec succès',
        'search_group_fetch_successfully' => 'Groupe de recherche récupéré avec succès',
        'search_events_successfully' => 'Événements de recherche récupérés avec succès',
        'search_jobs_successfully' => 'Emplois de recherche récupérés avec succès',
        'package_not_exist' => 'Le forfait n existe pas',
        'already_subscribed' => 'Vous êtes déjà abonné à un forfait de niveau supérieur ou égal.',
        'subscription_success' => 'Abonnement au forfait réussi.',
        'package_id_label' => 'ID du forfait',
        'package_id_required' => 'L ID du forfait est requis.',
        'package_upgrade_not_allowed' => 'Nous ne pouvons pas mettre à niveau ce forfait car votre abonnement de niveau supérieur est déjà actif.',
        'package_subscription_success' => 'Abonnement au forfait réussi.',
        'package_already_subscribed' => 'Forfait déjà abonné.',
        'account_deletion_not_available' => 'La suppression de compte n est pas disponible.',
        'incorrect_password' => 'Mot de passe incorrect.',
        'account_deleted_successfully' => 'Le compte est supprimé avec succès.',
        'account_deletion_failed' => 'Échec de la suppression du compte',
        'user_fetch_successfully' => 'Utilisateur récupéré avec succès',
        'pro_user_not_found' => 'Utilisateur Pro non trouvé.',
        'poke_successfully' => 'Piqué avec succès',
        'poked_you' =>'vous a piqué',
        'blood_donor_found' => 'Donneur de sang trouvé avec succès',
        'blood_donor_not_found' => 'Donneur de sang non trouvé',
        'blood_request_added_successfully' => 'Demande de sang ajoutée avec succès',
        'blood_group_required' => 'Le groupe sanguin est requis.',
        'cannot_transfer_to_self' => 'Impossible de transférer sur votre propre compte',
        'amount_transferred_successfully' => 'Montant transféré avec succès',
        'transfer_failed_due_to' => 'Le montant ne peut pas être transféré en raison de : ',
        'fund_id_required' => 'L ID du fonds est requis',
        'donation_not_found' => 'Don non trouvé',
        'donation_successful' => 'Don effectué avec succès',
        'donation_failed_due_to' => 'Le montant ne peut pas être transféré en raison de : ',
        'profile_updated_successfully'=>'Profil mis à jour avec succès',
        'admin_withdraw_error'=>'L administrateur ne peut pas créer de retrait',
        'paypal_withdraw_success'=>'Retrait via PayPal créé avec succès',
        'bank_withdraw_success'=>'Retrait via la banque créé avec succès',
        'minimum_less_than_maximum' => 'Le salaire minimum doit être inférieur au salaire maximum.',
        'job_title_invalid' => 'Le titre du poste ne peut contenir que des lettres anglaises et des espaces.',
        'job_created_successfully' => 'Emploi créé avec succès.',
        'moduleid_required' => "L'ID du module est requis.",
        'modulename_required' => 'Le nom du module est requis.',
        'already_reported' => 'Vous avez déjà signalé cet élément.',
        'cannot_report_your_own_product' => 'Vous ne pouvez pas signaler votre propre produit.',
        'cannot_report_your_own_event' => 'Vous ne pouvez pas signaler votre propre événement.',
        'cannot_report_your_own_page' => 'Vous ne pouvez pas signaler votre propre page.',
        'cannot_report_your_own_group' => 'Vous ne pouvez pas signaler votre propre groupe.',
        'reported_successfully' => 'Signalé avec succès.',
        'module_not_found' => 'Module non trouvé.',
        'cannot_report_your_own_job' => 'Vous ne pouvez pas signaler votre propre travail.',
        'cannot_report_your_own_space' => 'Vous ne pouvez pas signaler votre propre espace.',
        'is_paid_required' => '"Payé" est requis.',
        'course_price_not_zero' => 'Le prix du cours ne peut pas être nul.',
        'id_required' => "L'identifiant est requis",
        'format_required' => 'Le format est requis',
        'not_donation_post' => 'Ce post nest pas un post de don.',
        'funding_not_found' => 'Détails de financement introuvables.',
        'funding_list_fetch_success' => 'Détails de financement récupérés avec succès.',
        'type_error' => 'Type non reconnu',
        'lang_not_found' => 'Langue introuvable',
        'translate_failed' => 'Échec de la traduction du texte',
        'fetch_pokes_success' => 'Pokes récupérés avec succès!!!',
        'poke_not_found' => 'Aucun historique de poke trouvé',
        'movie_id_required' => 'LID du film est requis.',
        'movie_not_found' => 'Le film demandé est introuvable.',
        'movie_found_success' => 'Détails du film récupérés avec succès.',
        'recipient_id' => 'ID du destinataire',
        'tag_id_required' => 'LID de balise est requis.',
        'blog_not_found' => 'Aucun blog trouvé pour cette balise.',
        'facilities_fetch_success' => 'Installations récupérées avec succès.',
        'facilities_fetch_failed' => 'Échec de la récupération des installations.',
        'business_categories_fetch_success' => 'Catégories dentreprises récupérées avec succès.',
        'business_categories_fetch_failed' => 'Échec de la récupération des catégories dentreprises.',
        
];
