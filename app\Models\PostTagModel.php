<?php

namespace App\Models;

use CodeIgniter\Model;

class PostTagModel extends Model
{
    protected $table            = 'post_tags';
    protected $primaryKey       = 'id';
    protected $useAutoIncrement = true;
    protected $returnType       = 'array';
    protected $useSoftDeletes   = false;
    protected $protectFields    = false;
    protected $allowedFields    = [];

    // Dates
    protected $useTimestamps = false;
    protected $dateFormat    = 'datetime';
    protected $createdField  = 'created_at';
    protected $updatedField  = 'updated_at';
    protected $deletedField  = 'deleted_at';

    // Validation
    protected $validationRules      = [];
    protected $validationMessages   = [];
    protected $skipValidation       = false;
    protected $cleanValidationRules = true;

    // Callbacks
    protected $allowCallbacks = true;
    protected $beforeInsert   = [];
    protected $afterInsert    = [];
    protected $beforeUpdate   = [];
    protected $afterUpdate    = [];
    protected $beforeFind     = [];
    protected $afterFind      = [];
    protected $beforeDelete   = [];
    protected $afterDelete    = [];
    public function deleteposttags($post_id)
    {
        $this->deleterealTags($post_id);
        return $this->where('post_id', $post_id)->delete();
    }
  
    public function deleterealTags($post_id)
    {
        $tags = $this->where('post_id', $post_id)->findAll();
        foreach ($tags as $tag) {
            $otherpostTagCount =  $this->where('post_id!=', $post_id)->where('tag_id',$tag['tag_id'])->countAllResults();
            if ($otherpostTagCount == 0) {
                $tagModel = new HashTagModel();
                $tagModel->delete($tag['tag_id']);
            }
        }
    }
}
