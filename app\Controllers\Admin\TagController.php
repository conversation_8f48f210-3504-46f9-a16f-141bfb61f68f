<?php

namespace App\Controllers\Admin;

use App\Controllers\BaseController;
use App\Models\TagModel;

class TagController extends BaseController
{
    public function index()
    {
        $this->data['page_title'] = lang('Admin.tags');
        $tagModel = new TagModel();
        $this->data['page'] = $this->request->getVar('page') ?? 1;
        $perPage = 20;
        $this->data['tags'] = $tagModel->paginate($perPage);
        $this->data['pager'] = $tagModel->pager;
        $this->data['perPage'] = $perPage;
        $this->data['breadcrumbs'][] = ['name' => lang('Admin.manage_tags'), 'url' => ''];

        return view('admin/pages/tags/index', $this->data);
    }

    public function store()
    {
        $validationRules = [
            'name' => [
                'label' => lang('Admin.tag_name'),
                'rules' => 'required',
                'errors' => [
                    'required' => lang('Admin.tag_name_required'),
                ],
            ],
        ];

        if (!$this->validate($validationRules)) {
            $this->data['page_title'] = lang('Admin.add_new_tag');
            $this->data['js_files'] = ['assets/js/jquery.validate.min.js'];
            $this->data['breadcrumbs'][] = ['name' => lang('Admin.add_new_tag'), 'url' => ''];
            $this->data['validation'] = $this->validator->getErrors();
            return view('admin/pages/tags/create', $this->data);
        }

        $data = ['name' => $this->request->getVar('name')];
        $tagModel = new TagModel();
        $res = $tagModel->save($data);
        $session = \Config\Services::session();

        if ($res) {
            $session->setFlashdata('success', lang('Admin.tag_created_success'));
            return redirect('admin/tags');
        } else {
            $session->setFlashdata('error', lang('Admin.tag_creation_failed'));
            return redirect('admin/tags/create');
        }
    }

    public function edit($id)
    {
        $this->data['page_title'] = lang('Admin.edit_tag');
        $tagModel = new TagModel();
        $this->data['js_files'] = ['assets/js/jquery.validate.min.js'];
        $this->data['breadcrumbs'][] = ['name' => lang('Admin.edit_tag'), 'url' => ''];
        $this->data['tag'] = $tagModel->find($id);
        return view('admin/pages/tags/edit', $this->data);
    }

    public function update($id)
    {
        $validationRules = [
            'name' => [
                'rules' => 'required',
                'errors' => [
                    'required' => lang('Admin.tag_name_required'),
                ],
            ],
        ];

        $session = \Config\Services::session();
        $tagModel = new TagModel();

        if (!$this->validate($validationRules)) {
            $this->data['js_files'] = ['assets/js/jquery.validate.min.js'];
            $this->data['breadcrumbs'][] = ['name' => lang('Admin.edit_tag'), 'url' => ''];
            $this->data['tag'] = $tagModel->find($id);
            $this->data['validation'] = $this->validator->getErrors();
            return view('admin/pages/tags/edit', $this->data);
        }

        $data = ['name' => $this->request->getVar('name')];
        $res = $tagModel->update($id, $data);

        if ($res) {
            $session->setFlashdata('success', lang('Admin.tag_updated_success'));
        } else {
            $session->setFlashdata('error', lang('Admin.tag_update_failed'));
        }

        return redirect('admin/tags');
    }

    public function delete($id)
    {
        $session = \Config\Services::session();
        $tagModel = new TagModel();

        if ($id > 10) {
            $res = $tagModel->delete($id);
            if ($res) {
                $session->setFlashdata('success', lang('Admin.tag_deleted_success'));
            } else {
                $session->setFlashdata('error', lang('Admin.tag_deletion_failed'));
            }
        } else {
            $session->setFlashdata('error', lang('Admin.cannot_delete_default_tag'));
        }

        return redirect('admin/tags');
    }
}
