<?php

namespace App\Models;

use CodeIgniter\Model;

class Course extends Model
{
    protected $table            = 'courses';
    protected $primaryKey       = 'id';
    protected $useAutoIncrement = true;
    protected $returnType       = 'array';
    protected $useSoftDeletes   = true;
    protected $protectFields    = false;
    protected $allowedFields    = [];

    // Dates
    protected $useTimestamps = true;
    protected $dateFormat    = 'datetime';
    protected $createdField  = 'created_at';
    protected $updatedField  = 'updated_at';
    protected $deletedField  = 'deleted_at';

    // Validation
    protected $validationRules      = [];
    protected $validationMessages   = [];
    protected $skipValidation       = false;
    protected $cleanValidationRules = true;

    // Callbacks
    protected $allowCallbacks = true;
    protected $beforeInsert   = [];
    protected $afterInsert    = [];
    protected $beforeUpdate   = [];
    protected $afterUpdate    = [];
    protected $beforeFind     = [];
    protected $afterFind      = [];
    protected $beforeDelete   = [];
    protected $afterDelete    = [];
    public function courseDetail($course,$user_id)
    {
        $levelArray = [
            1=>'Beginner',
            2=>'Intermediate',
            3=>'Advanced'
        ];
        $courseApplicantModel  = New CourseApplicant();
        $userModel  =  New UserModel();
        $course['cover'] = getMedia($course['cover']);
        $course['is_applied'] = $courseApplicantModel->checkAlreadyApplied($user_id,$course['id']);
        $course['is_paid'] = ($course['is_paid']==0)?'Free':'Paid';
        $course['level'] = isset($levelArray[$course['level']]) ? $levelArray[$course['level']] : 'Other';
        $course['user'] = $userModel->getUserShortInfo($course['created_by']);
        return $course;   
    }
    public function getAllCourses()
    {
        $db = $this->db;
        $dbPrefix = $db->DBPrefix;
        return $this->select([$dbPrefix.'users.username', $dbPrefix.'courses.*'])
            ->join($dbPrefix.'users', $dbPrefix.'users.id = '.$dbPrefix.'courses.created_by')
            ->where($dbPrefix.'users.deleted_at', null)
            ->findAll();
    }
    public function  getCourseAllApplicants($id)
    {
        $userIds = $this->select('user_id')->where('course_id',$id)->findAll();
        if(count($userIds)>0)
        {
            $userIds = array_column($userIds,'user_id');
            $userModel  =  New UserModel();
            $users[] = $userModel->getUserShortInfo($userIds);
            return $users;
        }
        return null;
    }
    public function deleteCourseByUserId($id)
    {
        $course = $this->where('created_by',$id)->findAll();
        $courseApplicant = new CourseApplicant();
        if ($course) {
            foreach ($course as $c) {
                $this->delete($c['id']);
                $courseApplicant->where('course_id',$c['id'])->delete();
            }
        }
        $courseApplicant->where('user_id',$id)->delete();
        return true;
    }
}
