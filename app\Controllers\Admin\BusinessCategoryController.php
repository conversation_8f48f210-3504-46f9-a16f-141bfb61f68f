<?php

namespace App\Controllers\Admin;

use App\Models\BusinessCategory;
use App\Controllers\BaseController;

class BusinessCategoryController extends AdminBaseController
{
    public function index()
    {
        $this->data['page_title'] = lang('Admin.business_categories');
        $businessCategoryModel = new BusinessCategory();

        $this->data['page'] = $this->request->getVar('page') ?? 1;
        $perPage = 20;
        $this->data['categories'] = $businessCategoryModel->paginate($perPage);
        $this->data['pager'] = $businessCategoryModel->pager;
        $this->data['perPage'] = $perPage;
        $this->data['breadcrumbs'][] = ['name' => lang('Admin.manage_business_categories'), 'url' => ''];

        return view('admin/pages/businesscategories/index', $this->data);
    }

    public function store()
    {
        $validationRules = [
            'name' => [
                'label' => 'Name',
                'rules' => 'required',
                'errors' => [
                    'required' => lang('Admin.categoryname_required'),
                ],
            ],
        ];

        if (!$this->validate($validationRules)) {
            $this->data['page_title'] = lang('Admin.add_new_business_category');
            $this->data['js_files'] = ['assets/js/jquery.validate.min.js'];
            $this->data['breadcrumbs'][] = ['name' => lang('Admin.business_category'), 'url' => ''];
            $this->data['validation'] = $this->validator->getErrors();
            return view('admin/pages/businesscategories/index', $this->data);
        }

        $data = [
            'name' => $this->request->getVar('name'),
        ];

        $businessCategoryModel = new BusinessCategory();
        $res = $businessCategoryModel->save($data);
        $session = \Config\Services::session();

        if ($res) {
            $session->setFlashdata('success', lang('Admin.category_created_success'));
        } else {
            $session->setFlashdata('error', lang('Admin.category_creation_failed'));
        }

        return redirect('admin/business-categories');
    }

    public function edit($id)
    {
        $this->data['page_title'] = lang('Admin.edit_business_category');
        $businessCategoryModel = new BusinessCategory();
        $this->data['js_files'] = ['assets/js/jquery.validate.min.js'];
        $this->data['breadcrumbs'][] = ['name' => lang('Admin.edit_business_category'), 'url' => ''];
        $this->data['category'] = $businessCategoryModel->find($id);

        return view('admin/pages/businesscategories/edit', $this->data);
    }

    public function update($id)
    {
        $validationRules = [
            'name' => [
                'rules' => 'required',
                'errors' => [
                    'required' => lang('Admin.categoryname_required'),
                ],
            ],
        ];

        $session = \Config\Services::session();
        $businessCategoryModel = new BusinessCategory();

        if (!$this->validate($validationRules)) {
            $this->data['js_files'] = ['assets/js/jquery.validate.min.js'];
            $this->data['breadcrumbs'][] = ['name' => lang('Admin.business_category'), 'url' => ''];
            $this->data['category'] = $businessCategoryModel->find($id);
            $this->data['validation'] = $this->validator->getErrors();

            return view('admin/pages/businesscategories/edit', $this->data);
        }

        $data = [
            'name' => $this->request->getVar('name'),
        ];

        $res = $businessCategoryModel->update($id, $data);

        if ($res) {
            $session->setFlashdata('success', lang('Admin.category_updated_success'));
        } else {
            $session->setFlashdata('error', lang('Admin.category_update_failed'));
        }

        return redirect('admin/business-categories');
    }

    public function delete($id)
    {
        $session = \Config\Services::session();
        $businessCategoryModel = new BusinessCategory();
        if ($id > 10) {
            $res = $businessCategoryModel->delete($id);
            if ($res) {
                $session->setFlashdata('success', lang('Admin.category_deleted_success'));
            } else {
                $session->setFlashdata('error', lang('Admin.category_deletion_failed'));
            }
        } else {
            $session->setFlashdata('error', lang('Admin.can_not_delete'));
        }
        return redirect('admin/business-categories');
    }

}
