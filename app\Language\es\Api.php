<?php
return [
    'validation_error' => 'Error de Validación',
'token_generated_success' => 'Token generado con éxito.',
'channel_name_required' => 'El campo Nombre del Canal es obligatorio.',
'to_user_id_required' => 'El ID de usuario de destino es obligatorio.',
'user_is_live' => 'El usuario está en vivo',
'live_stream_ended' => 'Transmisión en vivo finalizada',
'is_live_notification' => 'está en vivo',
'is_calling_you' => 'te está llamando',
'type_required' => 'El campo Tipo es obligatorio.',
'call_history_fetch_success' => 'Historial de llamadas recuperado con éxito.',
'history_not_found' => 'Historial no encontrado.',
'stream_not_found' => 'Transmisión no encontrada.',
'live_stream_join_success' => 'Transmisión en vivo unida con éxito.',
'already_joined_stream' => 'Ya te has unido a esta transmisión.',
'course_updated_successfully' => 'El curso se ha actualizado con éxito.',
'post_id_required' => 'Se requiere el ID de la publicación.',
'post_not_found' => 'La publicación solicitada no se encontró.',
'not_donation_post' => 'Esta publicación no es una publicación de donación.',
'funding_not_found' => 'Detalles de financiación no encontrados.',
'funding_list_fetch_success' => 'Detalles de financiación obtenidos con éxito.',
'token_fetch_success' => 'Token recuperado con éxito.',
'notification_sent_success' => 'Notificación enviada con éxito.',
'call_declined' => 'rechazó tu llamada',
'user_id_required' => 'El campo ID de usuario es obligatorio.',
'livestream_request_accepted' => 'solicitud de transmisión en vivo aceptada',
'live_stream_users_fetch_success' => 'Usuarios de transmisión en vivo recuperados con éxito.',
'no_live_stream_users_found' => 'No se encontraron usuarios de transmisión en vivo.',
'user_already_in_livestream' => 'El usuario ya está en la transmisión en vivo.',
'user_added_to_livestream' => 'Usuario añadido a la transmisión en vivo con éxito.',
'user_not_in_livestream' => 'El usuario no está en la transmisión en vivo.',
'user_removed_from_livestream' => 'Usuario eliminado de la transmisión en vivo con éxito.',
'invalid_choice' => 'Elección inválida.',
'live_stream_user_found_success' => 'Usuarios de transmisión en vivo encontrados con éxito.',
'user_fetch_success' => 'Usuarios recuperados con éxito.',
'user_not_found' => 'Usuario no encontrado.',
'cannot_send_gift_to_self' => 'No puedes enviarte el regalo a ti mismo.',
'gift_not_found' => 'Regalo no encontrado.',
'insufficient_balance' => 'Saldo insuficiente.',
'gift_sent_success' => 'Regalo enviado con éxito.',
'gift_id_required' => 'El campo ID de Regalo es obligatorio.',
'call_not_found' => 'Llamada no encontrada.',
'unauthenticated' => 'No autenticado.',
'call_deleted_success' => 'Llamada eliminada con éxito.',
'call_id_required' => 'El campo ID de Llamada es obligatorio.',
'course_deleted_successfully' => 'El curso se ha eliminado con éxito.',
'call_history_deleted_success' => 'Historial de llamadas eliminado con éxito.',
'create_course_success' => 'El curso ha sido creado con éxito.',
'email_required' => 'El campo Correo Electrónico es obligatorio.',
'email_invalid' => 'Por favor, proporciona una dirección de correo electrónico válida.',
'password_required' => 'El campo Contraseña es obligatorio.',
'password_min_length' => 'La contraseña debe tener al menos 6 caracteres.',
'email_password_mismatch' => 'El correo electrónico o la contraseña no coinciden.',
'account_verification' => 'Por favor, revisa tu correo electrónico para el enlace de verificación. Verifica tu carpeta de spam también.',
'login_success' => 'Inicio de sesión exitoso.',
'email_unique' => 'Esta dirección de correo electrónico ya está registrada. Por favor, usa otro correo electrónico.',
'password_confirm_mismatch' => 'Las contraseñas no coinciden.',
'date_of_birth_required' => 'El campo Fecha de Nacimiento es obligatorio.',
'gender_required' => 'El campo Género es obligatorio.',
'registration_success' => 'Registrado con éxito. Por favor, inicia sesión.',
'demo_restriction' => 'No puedes realizar esta acción en modo demo.',
'file_upload_success' => 'Datos actualizados con éxito.',
'site_logo_upload_error' => 'Hubo un error al cargar el logo del sitio.',
'favicon_upload_error' => 'Hubo un error al cargar el favicon.',
'noFriendRequests' => 'No se encontró solicitud de amistad.',
'block_self_error' => 'No puedes bloquearte a ti mismo.',
'user_unblocked_success' => 'Usuario desbloqueado con éxito.',
'user_blocked_success' => 'Usuario bloqueado con éxito.',
'block_user_fetch_success' => 'Usuario bloqueado recuperado con éxito.',
'block_user_not_found' => 'Usuario bloqueado no encontrado.',
'user_already_reported' => 'Ya has reportado a este usuario.',
'user_reported_success' => 'Usuario reportado con éxito.',
'report_user_id_required' => 'El campo ID de Usuario Reportado es obligatorio.',
'reason_required' => 'El campo Razón es obligatorio.',
'blog_fetch_success' => 'Blogs recuperados con éxito.',
'no_blog_found' => 'No se encontraron blogs.',
'tags_fetch_success' => 'Etiquetas recuperadas con éxito.',
'blood_request_not_found' => 'Solicitud de sangre no encontrada.',
'unauthenticated_error' => 'No estás autorizado para eliminar esta solicitud de sangre.',
'blood_request_delete_success' => 'Solicitud de sangre eliminada con éxito.',
'messages_fetch_success' => 'Mensajes recuperados con éxito.',
'no_messages_found' => 'No se encontraron mensajes.',
'error_occurred' => 'Ocurrió un error: {0}',
'to_id_required' => 'El campo ID del destinatario es obligatorio.',
'to_id_integer' => 'El campo ID del destinatario debe ser un número entero válido.',
'chat_list_fetch_success' => 'Lista de chat recuperada con éxito.',
'no_chats_found' => 'No se encontraron chats para la página dada.',
'page_id_required' => 'El campo ID de la página es obligatorio.',
'page_id_integer' => 'El campo ID de la página debe ser un número entero válido.',
'message_deleted_success' => 'El mensaje ha sido eliminado con éxito.',
'not_allowed_to_delete_message' => 'No tienes permiso para eliminar este mensaje.',
'message_not_found' => 'Mensaje no encontrado.',
'chat_users_fetched_success' => 'Usuarios de chat recuperados con éxito.',
'paypal_deposit_success' => 'Cantidad depositada usando PayPal con éxito.',
'stripe_deposit_success' => 'Cantidad depositada usando Stripe con éxito.',
'paystack_deposit_success' => 'Cantidad depositada usando Paystack con éxito.',
'flutterwave_deposit_success' => 'Cantidad depositada usando Flutterwave con éxito.',
'gateway_id_required' => 'La pasarela de pago es obligatoria.',
'transaction_id_required' => 'El ID de la transacción es obligatorio.',
'event_created_success' => 'Evento creado con éxito.',
'name_required' => 'El nombre del evento es obligatorio.',
'name_invalid' => 'El nombre del evento solo puede contener caracteres alfanuméricos y espacios.',
'location_required' => 'La ubicación del evento es obligatoria.',
'start_date_required' => 'La fecha de inicio es obligatoria.',
'start_time_required' => 'La hora de inicio es obligatoria.',
'end_date_required' => 'La fecha de finalización es obligatoria.',
'end_time_required' => 'La hora de finalización es obligatoria.',
'fetch_events_success' => 'Eventos recuperados con éxito.',
'events_not_found' => 'No se encontraron eventos.',
'fetch_interested_success' => 'Eventos de interés recuperados con éxito.',
'no_interest_events_found' => 'No se encontraron eventos de interés.',
'fetch_going_success' => 'Eventos a los que se va recuperados con éxito.',
'going_events_not_found' => 'No se encontraron eventos a los que se va.',
'fetch_my_events_success' => 'Mis eventos recuperados con éxito.',
'my_events_not_found' => 'No se encontraron eventos para ti.',
'event_id_required' => 'El ID del evento es obligatorio.',
'interest_marked_as_not_interested' => 'Evento marcado como no interesado.',
'interest_marked_as_interested' => 'Evento marcado como interesado.',
'going_marked_as_not_going' => 'Evento marcado como no asistente.',
'going_marked_as_going' => 'Evento marcado como asistente.',
'event_update_success' => 'Campos del evento actualizados con éxito.',
'event_update_failure' => 'Error al actualizar el evento.',
'not_allowed' => 'No tienes permiso para realizar esta acción.',
'event_not_found' => 'Evento no encontrado.',
'event_deleted_success' => 'Evento eliminado con éxito.',
'request_action_required' => 'Se requiere una acción para la solicitud.',
'friend_request_accepted' => 'Solicitud de amistad aceptada.',
'friend_request_declined' => 'Solicitud de amistad rechazada.',
'friend_request_not_found' => 'Solicitud de amistad no encontrada.',
'userNotFound' => 'Usuario no encontrado',
'privacySettings' => 'No se puede proceder con la solicitud debido a las configuraciones de privacidad.',
'requestCancelled' => 'Solicitud de amistad cancelada.',
'alreadyFriends' => 'Ya son amigos.',
'pendingRequest' => 'Tienes una solicitud de amistad pendiente de este usuario.',
'requestSent' => 'Solicitud de amistad enviada con éxito.',
'validationError' => 'Error de Validación',
'apiKeyUnauthorized' => 'No autorizado',
'friend_two_required' => 'El campo friend_two es obligatorio.',
'request_id_required' => 'El campo request_id es obligatorio.',
'requestSuccessfullyDeleted' => 'Solicitud de amistad eliminada con éxito.',
'requestNotFound' => 'Solicitud de amistad no encontrada.',
'unfriendSuccess' => 'Desamigo con éxito.',
'friendRequestNotFound' => 'Solicitud de amistad no encontrada.',
'noRecommendations' => 'No se encontraron recomendaciones.',
'recommendationsFound' => 'Recomendaciones encontradas.',
'role_updated_success' => 'Rol de amigo actualizado',
'friend_list_fetch' => 'Lista de amigos recuperada con éxito',
'friend_not_found' => 'Actualmente no tienes amigos.',
'sent_requests_fetched' => 'Solicitudes enviadas recuperadas con éxito.',
'no_sent_requests' => 'No tienes solicitudes enviadas.',
'fetch_games_success' => 'Datos de juegos recuperados con éxito.',
'no_games_found' => 'No se encontraron juegos.',
'groups_fetched_successfully' => 'Grupos recuperados con éxito.',
'no_groups_found' => 'No se encontraron grupos.',
'user_groups_fetched_successfully' => 'Grupos de usuario recuperados con éxito.',
'no_user_groups_found' => 'No se encontraron grupos de usuario.',
'group_title_required' => 'El título del grupo es obligatorio.',
'group_title_invalid_characters' => 'El título del grupo contiene caracteres inválidos.',
'about_group_required' => 'Acerca del grupo es obligatorio.',
'category_required' => 'La categoría es obligatoria.',
'privacy_required' => 'La configuración de privacidad es obligatoria.',
'group_created_successfully' => 'Grupo creado con éxito.',
'group_id_required' => 'El ID del grupo es obligatorio.',
'group_data_fetch_success' => 'Datos del grupo recuperados con éxito.',
'group_data_not_found' => 'Datos del grupo no encontrados.',
'group_update_success' => 'Campos del grupo actualizados con éxito.',
'unauthorized_access' => 'No tienes permiso para realizar esta acción.',
'group_not_found' => 'Grupo no encontrado.',
'already_member' => 'Ya eres miembro de este grupo.',
'group_join_success' => 'Grupo unido con éxito.',
'not_member' => 'No eres miembro del grupo.',
'group_left_success' => 'Has salido del grupo con éxito.',
'no_data_found' => 'No se encontraron datos.',
'member_already_in_group' => 'Este miembro ya está en el grupo.',
'member_added_successfully' => 'Miembro añadido con éxito.',
'user_not_member' => 'Este usuario no es miembro del grupo.',
'member_removed_successfully' => 'Miembro eliminado con éxito.',
'group_members_fetched_successfully' => 'Miembros del grupo recuperados con éxito.',
'group_members_not_found' => 'Miembros del grupo no encontrados.',
'admin_creation_success' => 'Administrador del grupo creado con éxito.',
'not_group_admin' => 'Este usuario no es un administrador del grupo.',
'admin_dismiss_success' => 'Administrador del grupo despedido con éxito.',
'groups_fetch_success' => 'Grupos recuperados con éxito.',
'user_groups_not_found' => 'Grupos de usuario no encontrados.',
'job_id_required' => 'El ID del trabajo es obligatorio',
'job_title_required' => 'El título del trabajo es obligatorio.',
'job_description_required' => 'La descripción del trabajo es obligatoria.',
'job_location_required' => 'La ubicación del trabajo es obligatoria.',
'minimum_salary_required' => 'El salario mínimo es obligatorio.',
'maximum_salary_required' => 'El salario máximo es obligatorio.',
'currency_required' => 'La moneda es obligatoria.',
'salary_date_required' => 'La fecha de salario es obligatoria.',
'experience_years_required' => 'Los años de experiencia son obligatorios.',
'my_jobs_fetched_successfully' => 'Mis trabajos recuperados con éxito.',
'all_jobs_fetched_successfully' => 'Todos los trabajos recuperados con éxito.',
'no_job_found' => 'No se encontró trabajo.',
'application_successful' => 'Solicitud enviada con éxito.',
'already_applied' => 'Ya has aplicado para este trabajo.',
'job_id_integer' => 'El ID del trabajo debe ser un número entero.',
'phone_required' => 'El número de teléfono es obligatorio.',
'phone_invalid' => 'El número de teléfono es inválido.',
'cv_file_optional' => 'El archivo CV es opcional.',
'cv_file_uploaded' => 'El archivo CV debe ser cargado.',
'cv_file_max_size' => 'El tamaño del archivo CV no debe exceder los 2 MB.',
'cv_file_mime_in' => 'El archivo CV debe ser un documento PDF o Word.',
'applied_successfully' => 'Aplicado con éxito.',
'already_applied_for_job' => 'Ya has aplicado para este trabajo.',
'search_parameters_missing' => 'Por favor, ingresa el tipo o título del trabajo.',
'search_success' => 'Trabajos encontrados con éxito.',
'no_jobs_found' => 'No se encontraron trabajos.',
'job_update_success' => 'Campos del trabajo actualizados con éxito.',
'unauthorized' => 'No tienes permiso.',
'job_not_found' => 'Trabajo no encontrado.',
'job_categories_fetch_success' => 'Categorías de trabajos recuperadas con éxito.',
'no_job_category_found' => 'No se encontraron categorías de trabajo.',
'fetch_applied_candidates_success' => 'Candidatos aplicados recuperados con éxito.',
'candidates_not_found' => 'Candidatos no encontrados.',
'fetch_notifications_success' => 'Notificaciones recuperadas con éxito.',
'no_notifications_found' => 'No se encontraron notificaciones.',
'notifications_list_success' => 'Lista de notificaciones recuperada con éxito.',
'all_notifications_marked_as_read' => 'Todas las notificaciones han sido marcadas como leídas.',
'all_notifications_deleted_successfully' => 'Todas las notificaciones han sido eliminadas con éxito.',
'notification_not_found' => 'Notificación no encontrada',
'notification_deleted_successfully' => 'Notificación eliminada con éxito',
'notification_updated_successfully' => 'Notificación actualizada con éxito.',
'job_deleted_successfully' => 'El trabajo ha sido eliminado con éxito.',
'transaction_failed' => 'Transacción fallida.',
'page_created_successfully' => 'La página ha sido creada con éxito',
'page_title_required' => 'El título de la página es obligatorio',
'page_title_min_length' => 'El título de la página debe tener al menos 5 caracteres',
'page_title_max_length' => 'El título de la página no puede exceder los 50 caracteres',
'page_title_invalid_characters' => 'El título de la página contiene caracteres inválidos',
'page_description_required' => 'La descripción de la página es obligatoria',
'page_category_required' => 'La categoría de la página es obligatoria',
'page_deleted_successfully' => 'La página ha sido eliminada con éxito',
'page_not_found' => 'Página no encontrada',
'permission_denied' => 'No tienes permiso para actualizar esta página',
'page_updated_successfully' => 'Campos de la página actualizados con éxito',
'email_subject' => 'Le Gustó Tu Página',
'email_body_liked_page' => 'Alguien le gustó tu página.',
'notification_liked_page' => 'le gustó tu página.',
'push_notification_liked_page' => 'le gustó tu página',
'page_successfully_liked' => 'Página gustada con éxito.',
'page_successfully_unliked' => 'Página sin gustar con éxito.',
'fetch_liked_pages_success' => 'Páginas gustadas recuperadas con éxito',
'no_liked_pages_found' => 'No se encontraron páginas gustadas',
'deleted_user_id_required' => 'El campo ID de Usuario Eliminado es obligatorio.',
'pages_fetch_success' => 'Páginas recuperadas con éxito.',
'user_removed' => 'El usuario ha sido eliminado con éxito.',
'post_text_required' => 'El texto de la publicación es obligatorio.',
'input_required' => 'Se requiere al menos uno de los siguientes: texto de la publicación, imágenes, audio o video.',
'not_a_group_member' => 'No eres miembro del grupo.',
'post_created_success' => 'Publicación creada con éxito',
'post_detail' => 'Detalle de la Publicación',
'post_saved_list' => 'Lista de publicaciones guardadas',
'success' => 'Éxito',
'post_id' => 'ID de la Publicación',
'validation_failed' => 'La validación falló.',
'post_detail_fetched' => 'Detalle de la publicación recuperado con éxito.',
'post_deleted_successfully' => 'Publicación eliminada con éxito.',
'unauthorized_to_delete_post' => 'No estás autorizado para eliminar esta publicación.',
'post_id_numeric' => 'El ID de la publicación debe ser numérico.',
'ad_title' => 'Título del Anuncio',
'title_required' => 'El título es obligatorio.',
'title_max_length' => 'El título no puede exceder los 150 caracteres.',
'ad_link' => 'Enlace del Anuncio',
'link_required' => 'El enlace es obligatorio.',
'link_max_length' => 'El enlace no puede exceder los 200 caracteres.',
'ad_body' => 'Cuerpo del Anuncio',
'body_required' => 'El cuerpo es obligatorio.',
'body_max_length' => 'El cuerpo no puede exceder los 250 caracteres.',
'image_upload_failed' => 'Error al cargar la imagen.',
'advertisement_added_successfully' => 'Anuncio añadido con éxito.',
'failed_to_add_advertisement' => 'Error al añadir el anuncio.',
'comment_text' => 'Texto del Comentario',
'comment_text_required' => 'El texto del comentario es obligatorio.',
'comment_added_successfully' => 'Comentario añadido con éxito.',
'failed_to_add_comment' => 'Error al añadir el comentario.',
'commented_on_post' => 'comentó en tu publicación.',
'post_comment' => 'Comentario de la Publicación',
'comments_fetched' => 'Comentarios recuperados con éxito.',
'comments_not_found' => 'No se encontraron comentarios para esta publicación.',
'comment_id' => 'ID del Comentario',
'comment_id_required' => 'El campo ID del Comentario es obligatorio.',
'comment_id_numeric' => 'El ID del comentario debe ser numérico.',
'comment_liked' => 'Comentario gustado con éxito.',
'comment_unliked' => 'Has dejado de gustar el comentario.',
'like_failed' => 'Error al dar me gusta al comentario.',
'new_comment_text' => 'Nuevo Texto del Comentario',
'new_comment_text_required' => 'El texto del comentario es obligatorio.',
'new_comment_text_string' => 'El texto del comentario debe ser una cadena válida.',
'comment_not_found' => 'Comentario no encontrado.',
'comment_update_permission_denied' => 'No tienes permiso para actualizar este comentario.',
'comment_updated_success' => 'Comentario actualizado con éxito.',
'comment_update_failed' => 'Error al actualizar el comentario.',
'reply_text' => 'Texto de la Respuesta',
'reply_text_required' => 'El texto de la respuesta es obligatorio.',
'reply_text_string' => 'El texto de la respuesta debe ser una cadena válida.',
'reply_added_successfully' => 'Respuesta añadida con éxito.',
'comment_reply_id' => 'ID de la Respuesta al Comentario',
'comment_reply_id_required' => 'El campo ID de la Respuesta al Comentario es obligatorio.',
'comment_reply_id_numeric' => 'El ID de la respuesta al comentario debe ser numérico.',
'already_liked_comment_reply' => 'Ya has dado me gusta a esta respuesta al comentario.',
'comment_reply_liked_successfully' => 'Respuesta al comentario gustada con éxito.',
'comment_reply_like_failed' => 'Error al dar me gusta a la respuesta al comentario.',
'reply_failed' => 'Error al añadir la respuesta al comentario.',
'post_shared_success' => 'La publicación ha sido compartida con éxito',
'shared_your_post' => 'compartió tu publicación',
'share_post_subject' => 'Compartir Publicación',
'server_error' => 'Ocurrió un error interno del servidor',
'comment_deleted_success' => 'Comentario eliminado con éxito.',
'post_saved_success' => 'Publicación guardada con éxito.',
'saved_post_deleted_success' => 'Publicación guardada eliminada con éxito.',
'action' => 'Acción',
'action_required' => 'La acción es obligatoria.',
'post_deleted_success' => 'Publicación eliminada con éxito.',
'unauthorized_delete' => 'No estás autorizado para eliminar esta publicación.',
'invalid_action' => 'Acción inválida.',
'post_reported_success' => 'Publicación reportada con éxito.',
'post_already_reported' => 'Ya has reportado esta publicación.',
'comments_disabled_success' => 'Comentarios deshabilitados con éxito.',
'comments_enabled_success' => 'Comentarios habilitados con éxito.',
'unauthorized_action' => 'No estás autorizado para realizar esta acción.',
'reaction_removed_success' => 'Reacción eliminada con éxito.',
'reaction_updated_success' => 'Reacción actualizada con éxito.',
'post_reaction_added_success' => 'Reacción a la publicación añadida con éxito.',
'reacted_on_your_post' => 'reaccionó a tu publicación.',
'post_reaction_not_found' => 'Reacción a la publicación no encontrada',
'shared_post_deleted' => 'La publicación compartida ha sido eliminada.',
'reply_id_required' => 'El campo ID de la Respuesta es obligatorio.',
'reply_deleted_success' => 'La respuesta al comentario ha sido eliminada con éxito.',
'comment_required' => 'El texto del comentario es obligatorio.',
'comment_reply_created_success' => 'Respuesta al comentario creada con éxito.',
'comment_replies_success' => 'Respuestas al comentario recuperadas con éxito.',
'great_job_already_assigned' => 'Buen trabajo ya asignado.',
'own_post_great_job' => 'Esta es tu propia publicación.',
'insufficient_balance_great_job' => 'Saldo insuficiente para otorgar Buen Trabajo.',
'great_job_awarded_success' => 'Buen Trabajo otorgado con éxito.',
'insufficient_balance_coc' => 'Saldo insuficiente para otorgar Taza de Café.',
'coc_already_assigned' => 'Taza de Café ya asignada.',
'cannot_award_own_post_coc' => "No puedes otorgar Taza de Café a tu propia publicación.",
'cup_of_coffee_awarded_success' => 'Taza de Café otorgada con éxito.',
'error' => 'Error',
'ad_not_found' => 'Anuncio no encontrado',
'ad_approved' => 'Tu solicitud de anuncio ha sido aprobada',
'ad_approve_success' => 'Solicitud de anuncio aprobada con éxito',
'ad_not_approved_balance' => 'Tu solicitud de anuncio no ha sido aprobada debido a saldo insuficiente.',
'ad_approve_fail_balance' => 'La solicitud de anuncio no se puede aprobar debido a saldo insuficiente',
'ad_rejected' => 'Tu solicitud de anuncio ha sido rechazada',
'ad_reject_success' => 'Solicitud de anuncio rechazada con éxito',
'ad_id_required' => 'El campo ID del anuncio es obligatorio',
'privacy_changed' => 'La privacidad de la publicación se cambió a {privacy}',
'privacy_public' => 'Público',
'privacy_friends' => 'Amigos',
'privacy_only_me' => 'Solo yo',
'privacy_family' => 'Familia',
'privacy_business' => 'Negocios',
'post_updated' => 'La publicación ha sido actualizada',
'advertisement_request_fetch_success' => 'Solicitud de anuncio de publicación recuperada con éxito',
'advertisement_request_not_found' => 'Solicitud de anuncio de publicación no encontrada',
'status_pending' => 'Pendiente',
'status_approved' => 'Aprobado',
'status_rejected' => 'Rechazado',
'poll_id_required' => 'El campo ID de la encuesta es obligatorio',
'poll_id_integer' => 'El ID de la encuesta debe ser un número entero',
'poll_option_id_required' => 'El campo ID de la opción de encuesta es obligatorio',
'poll_option_id_integer' => 'El ID de la opción de encuesta debe ser un número entero',
'poll_not_found' => 'Encuesta no encontrada',
'poll_option_not_found' => 'Opción de encuesta no encontrada',
'already_voted' => 'Ya has votado, no puedes votar nuevamente',
'vote_successful' => 'Voto realizado con éxito',
'trending_hashtags_found' => 'Hashtags de tendencia encontrados con éxito',
'trending_hashtags_not_exist' => 'Los hashtags de tendencia no existen',
'amount_required' => 'La cantidad es obligatoria',
'cannot_feed_own_post' => 'No puedes alimentar tu propia publicación',
'product_name_required' => 'El nombre del producto es obligatorio',
'product_description_required' => 'La descripción del producto es obligatoria',
'price_required' => 'El precio es obligatorio',
'units_required' => 'Las unidades son obligatorias',
'images_required' => 'Las imágenes del producto son obligatorias',
'images_ext_in' => 'Las imágenes deben ser de tipo: png, jpg, jpeg',
'images_is_image' => 'El archivo debe ser una imagen válida',
'product_added_successfully' => 'Producto añadido con éxito',
'internal_server_error' => 'Error Interno del Servidor',
'validation_errors' => 'Errores de validación',
'post_feded_successfully' => 'Publicación alimentada con éxito',
'product_not_found' => 'Producto no encontrado',
'fetch_user_product_success' => 'Producto de usuario recuperado con éxito',
'invalid_user_id' => 'ID de usuario inválido',
'product_id_required' => 'El ID del producto es obligatorio',
'product_updated_successfully' => 'Campos del producto actualizados con éxito',
'product_deleted_successfully' => 'El producto ha sido eliminado',
'privacy_integer' => 'La configuración de privacidad debe ser un número entero',
'description_required' => 'La descripción es obligatoria',
'space_created_successfully' => 'El espacio ha sido creado con éxito',
'space_id_required' => 'El ID del espacio es obligatorio',
'space_updated_successfully' => 'Campos del espacio actualizados con éxito',
'space_not_found' => 'Espacio no encontrado',
'space_deleted_successfully' => 'Espacio eliminado con éxito',
'cannot_join_own_space' => 'No puedes unirte a tu propio espacio',
'already_member_of_space' => 'Ya eres miembro de este espacio',
'space_joined_successfully' => 'Espacio unido con éxito',
'not_member_of_space' => 'No eres miembro de este espacio',
'space_left_successfully' => 'Espacio dejado con éxito',
'already_cohost' => 'Ya eres co-anfitrión de este espacio',
'cohost_created_successfully' => 'Co-anfitrión creado con éxito',
'user_not_member_of_space' => 'El usuario no es miembro de este espacio',
'cohost_removed_successfully' => 'Co-anfitrión eliminado con éxito',
'not_a_cohost' => 'El usuario no es co-anfitrión de este espacio',
'spaces_fetched_successfully' => 'Espacios recuperados con éxito',
'spaces_data_fetched_successfully' => 'Datos de espacios recuperados con éxito',
'story_created_successfully' => 'La historia ha sido creada con éxito',
'stories_fetched_successfully' => 'Las historias se recuperaron con éxito',
'user_muted_successfully' => 'El usuario ha sido silenciado con éxito',
'user_unmuted_successfully' => 'El usuario ha sido reactivado con éxito',
'story_id_required' => 'El campo ID de la historia es obligatorio.',
'own_story' => 'Esta es tu propia historia',
'story_seen_successfully' => 'La historia ha sido vista con éxito',
'story_already_seen' => 'La historia ya ha sido vista',
'viewed_story_notification' => 'vio tu historia.',
'viewed_story_email_subject' => 'Historia Vista',
'viewed_story_email_body' => 'ha visto tu historia',
'story_seen_user_fetch_successfully' => 'Usuario que vio la historia recuperado con éxito',
'no_views_found' => 'No se encontraron vistas',
'story_deleted_successfully' => 'La historia ha sido eliminada con éxito',
'story_not_found' => 'Historia no encontrada',
'blocked_user' => 'Usuario Bloqueado',
'user_profile_fetch_successfully' => 'Perfil de usuario recuperado con éxito',
'profile_not_found' => 'Perfil no encontrado',
'viewed_your_profile' => 'vio tu perfil',
'view_profile_subject' => 'Ver Perfil',
'view_profile_text' => 'vio tu perfil',
'search_user_fetch_successfully' => 'Usuario buscado recuperado con éxito',
'search_group_fetch_successfully' => 'Grupo buscado recuperado con éxito',
'search_events_successfully' => 'Eventos buscados con éxito',
'search_jobs_successfully' => 'Trabajos buscados con éxito',
'package_not_exist' => 'El paquete no existe',
'already_subscribed' => 'Ya estás suscrito a un paquete de nivel superior o igual.',
'subscription_success' => 'Suscripción al paquete exitosa.',
'package_id_label' => 'ID del Paquete',
'package_id_required' => 'El ID del paquete es obligatorio.',
'package_upgrade_not_allowed' => 'No podemos actualizar este paquete ya que tu suscripción de nivel superior ya está activa.',
'package_subscription_success' => 'Suscripción al paquete con éxito.',
'package_already_subscribed' => 'Paquete ya suscrito.',
'account_deletion_not_available' => 'La eliminación de la cuenta no está disponible.',
'incorrect_password' => 'Contraseña incorrecta.',
'account_deleted_successfully' => 'La cuenta ha sido eliminada con éxito.',
'account_deletion_failed' => 'Error al eliminar la cuenta',
'user_fetch_successfully' => 'Usuario recuperado con éxito',
'pro_user_not_found' => 'Usuario Pro no encontrado.',
'poke_successfully' => 'Se realizó un poke con éxito',
'poked_you' => 'te hizo un poke',
'blood_donor_found' => 'Donante de sangre encontrado con éxito',
'blood_donor_not_found' => 'Donante de sangre no encontrado',
'blood_request_added_successfully' => 'Solicitud de sangre añadida con éxito',
'blood_group_required' => 'El grupo sanguíneo es obligatorio.',
'cannot_transfer_to_self' => 'No se puede transferir a tu propia cuenta',
'amount_transferred_successfully' => 'Cantidad transferida con éxito',
'transfer_failed_due_to' => 'La cantidad no puede ser transferida debido a: ',
'fund_id_required' => 'El ID de fondos es obligatorio',
'donation_not_found' => 'Donación no encontrada',
'donation_successful' => 'Donado con éxito',
'donation_failed_due_to' => 'La cantidad no puede ser transferida debido a: ',
'profile_updated_successfully' => 'Perfil actualizado con éxito',
'admin_withdraw_error' => 'El administrador no puede crear retiros',
'paypal_withdraw_success' => 'Retiro usando PayPal creado con éxito',
'bank_withdraw_success' => 'Retiro usando banco creado con éxito',
'minimum_less_than_maximum' => 'El salario mínimo debe ser menor que el salario máximo.',
'job_title_invalid' => 'El título del trabajo solo puede contener letras en inglés y espacios.',
'job_created_successfully' => 'Trabajo creado con éxito.',
'moduleid_required' => 'Se requiere el ID del módulo.',
'modulename_required' => 'Se requiere el nombre del módulo.',
'already_reported' => 'Ya has reportado este elemento.',
'cannot_report_your_own_product' => 'No puedes reportar tu propio producto.',
'cannot_report_your_own_event' => 'No puedes reportar tu propio evento.',
'cannot_report_your_own_page' => 'No puedes reportar tu propia página.',
'cannot_report_your_own_group' => 'No puedes reportar tu propio grupo.',
'reported_successfully' => 'Reportado con éxito.',
'module_not_found' => 'Módulo no encontrado.',
'cannot_report_your_own_job' => 'No puedes reportar tu propio trabajo.',
'cannot_report_your_own_space' => 'No puedes reportar tu propio espacio.',
'is_paid_required' => '"Pagado" es requerido.',
'course_price_not_zero' => 'El precio del curso no puede ser cero.',
'id_required' => 'Se requiere ID',
'format_required' => 'Se requiere formato',
'type_error' => 'Tipo no reconocido',   
'lang_not_found' => 'Idioma no encontrado',
'translate_failed' => 'No se pudo traducir el texto',
'fetch_pokes_success' => '¡Pokes obtenidos con éxito!!!',
'poke_not_found' => 'No se encontró historial de pokes',
'recipient_id' => 'ID del destinatario',
'tag_id_required' => 'Se requiere ID de etiqueta.',
'blog_not_found' => 'No se encontraron blogs para esta etiqueta.',
'facilities_fetch_success' => 'Instalaciones obtenidas con éxito.',
'facilities_fetch_failed' => 'Error al obtener las instalaciones.',
'business_categories_fetch_success' => 'Categorías de negocios obtenidas con éxito.',
'business_categories_fetch_failed' => 'Error al obtener las categorías de negocios.',
];
