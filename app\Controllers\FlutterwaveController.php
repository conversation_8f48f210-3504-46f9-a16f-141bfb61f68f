<?php

namespace App\Controllers;

use Exception;
use App\Controllers\BaseController;
use App\Controllers\DepositeController;

class FlutterwaveController extends BaseController
{
     public function initializePayment()
    {
        // Set up payment details
        $publicKey = get_setting('flutterwave_public_key');  // Use your actual public key
        $secretKey = get_setting('flutterwave_secret_key');  // Use your actual secret key
        $txRef = 'txref-' . uniqid();  // Unique transaction reference
        $amount = $this->request->getPost('amount');
        $email = $this->request->getPost('customer_email');
        $name = $this->request->getPost('customer_name');

        // Prepare data for Flutterwave payment link
        $data = [
            "tx_ref" => $txRef,
            "amount" => $amount,
            "currency" => "USD",
            "redirect_url" => base_url('payment/callback'),
            "payment_type" => "card",
            "customer" => [
                "email" => $email,
                "name" => $name
            ],
            "customizations" => [
                "title" => "Your Payment Title",
                "description" => "Payment Description"
            ]
        ];

        // Initialize cURL request to Flutterwave API
        $curl = curl_init();
        curl_setopt_array($curl, [
            CURLOPT_URL => "https://api.flutterwave.com/v3/payments",
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_CUSTOMREQUEST => "POST",
            CURLOPT_POSTFIELDS => json_encode($data),
            CURLOPT_HTTPHEADER => [
                "Authorization: Bearer $secretKey",
                "Content-Type: application/json"
            ]
        ]);

        $response = curl_exec($curl);
        $err = curl_error($curl);
        curl_close($curl);

        if ($err) {
            // Handle cURL error
            echo "cURL Error #:" . $err;
        } else {
            $result = json_decode($response);
            if ($result->status == 'success') {
                // Redirect to the payment link
                return redirect()->to($result->data->link);
            } else {
                // Handle API error
                echo "Error: " . $result->message;
            }
        }
    }
    public function callback()
    {
        try {

            $transaction_id = $this->request->getGet('transaction_id');
            $depositControllerObj = New DepositeController();
            
            $transaction = $depositControllerObj->depositUsingFlutterwave($transaction_id);
            $deposited_amount = $transaction['amount']-$transaction['app_fee']-$transaction['merchant_fee'];
            if($transaction['currency']!='USD')
            {
                $deposited_amount = $depositControllerObj->convertCurrency($transaction['currency'],$deposited_amount);
            }
            $depositControllerObj->depostAmount(4,$deposited_amount,$transaction_id,'USD');
            return redirect()->to(site_url('wallet'))->with('message', 'Payment successful');
        } catch (Exception $ex) {
            // Handle payment failure
            return redirect()->to('/wallet')->with('error', 'Payment execution failed');
        }
        
    }
}
