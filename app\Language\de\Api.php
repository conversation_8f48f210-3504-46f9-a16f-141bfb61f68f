<?php
return ['validation_error' => 'Validierungsfehler',
'token_generated_success' => 'Token erfolgreich generiert.',
'channel_name_required' => 'Das Feld für den Kanalnamen ist erforderlich.',
'to_user_id_required' => 'Die Benutzer-ID ist erforderlich.',
'user_is_live' => 'Benutzer ist live',
'live_stream_ended' => 'Live-Stream beendet',
'is_live_notification' => 'ist live',
'is_calling_you' => 'ruft Sie an',
'type_required' => 'Das Feld "Typ" ist erforderlich.',
'call_history_fetch_success' => 'Anrufhistorie erfolgreich abgerufen.',
'history_not_found' => 'Verlauf nicht gefunden.',
'stream_not_found' => 'Stream nicht gefunden.',
'live_stream_join_success' => 'Live-Stream erfolgreich beigetreten.',
'already_joined_stream' => 'Sie sind diesem Stream bereits beigetreten.',
'create_course_success' => 'Der Kurs wurde erfolgreich erstellt.',
'token_fetch_success' => 'Token erfolgreich abgerufen.',
'notification_sent_success' => 'Benachrichtigung erfolgreich gesendet.',
'call_declined' => 'hat Ihren Anruf abgelehnt',
'user_id_required' => 'Das Benutzer-ID-Feld ist erforderlich.',
'livestream_request_accepted' => 'Livestream-Anfrage akzeptiert',
'live_stream_users_fetch_success' => 'Live-Stream-Benutzer erfolgreich abgerufen.',
'no_live_stream_users_found' => 'Keine Live-Stream-Benutzer gefunden.',
'user_already_in_livestream' => 'Der Benutzer ist bereits im Livestream.',
'user_added_to_livestream' => 'Benutzer erfolgreich zum Livestream hinzugefügt.',
'user_not_in_livestream' => 'Benutzer ist nicht im Livestream.',
'user_removed_from_livestream' => 'Benutzer erfolgreich aus dem Livestream entfernt.',
'invalid_choice' => 'Ungültige Auswahl.',
'live_stream_user_found_success' => 'Live-Stream-Benutzer erfolgreich gefunden.',
'user_fetch_success' => 'Benutzer erfolgreich abgerufen.',
'user_not_found' => 'Benutzer nicht gefunden.',
'cannot_send_gift_to_self' => 'Sie können das Geschenk nicht an sich selbst senden.',
'gift_not_found' => 'Geschenk nicht gefunden.',
'insufficient_balance' => 'Unzureichendes Guthaben.',
'gift_sent_success' => 'Geschenk erfolgreich gesendet.',
'gift_id_required' => 'Das Geschenk-ID-Feld ist erforderlich.',
'call_not_found' => 'Anruf nicht gefunden.',
'unauthenticated' => 'Nicht authentifiziert.',
'call_deleted_success' => 'Anruf erfolgreich gelöscht.',
'call_id_required' => 'Das Anruf-ID-Feld ist erforderlich.',
'course_updated_successfully' => 'Der Kurs wurde erfolgreich aktualisiert.',
'course_deleted_successfully' => 'Der Kurs wurde erfolgreich gelöscht.',
'call_history_deleted_success' => 'Anrufverlauf erfolgreich gelöscht.',
'minimum_less_than_maximum' => 'Das Mindestgehalt muss geringer als das Höchstgehalt sein.',
'job_title_invalid' => 'Der Jobtitel darf nur englische Buchstaben und Leerzeichen enthalten.',
'email_required' => 'Das E-Mail-Feld ist erforderlich.',
'email_invalid' => 'Bitte geben Sie eine gültige E-Mail-Adresse ein.',
'password_required' => 'Das Passwortfeld ist erforderlich.',
'password_min_length' => 'Das Passwort muss mindestens 6 Zeichen lang sein.',
'email_password_mismatch' => 'E-Mail oder Passwort stimmen nicht überein.',
'account_verification' => 'Bitte überprüfen Sie Ihre E-Mail für den Bestätigungslink. Überprüfen Sie auch Ihren Spam-Ordner.',
'login_success' => 'Erfolgreich eingeloggt.',
'email_unique' => 'Diese E-Mail-Adresse ist bereits registriert. Bitte verwenden Sie eine andere E-Mail.',
'password_confirm_mismatch' => 'Passwörter stimmen nicht überein.',
'date_of_birth_required' => 'Das Geburtsdatum-Feld ist erforderlich.',
'gender_required' => 'Das Geschlechtsfeld ist erforderlich.',
'registration_success' => 'Erfolgreich registriert. Bitte einloggen.',
'demo_restriction' => 'Sie können diese Aktion im Demo-Modus nicht ausführen.',
'file_upload_success' => 'Daten erfolgreich aktualisiert.',
'site_logo_upload_error' => 'Beim Hochladen des Website-Logos ist ein Fehler aufgetreten.',
'favicon_upload_error' => 'Beim Hochladen des Favicons ist ein Fehler aufgetreten.',
'noFriendRequests' => 'Keine Freundschaftsanfragen gefunden',
'block_self_error' => 'Sie können sich nicht selbst blockieren.',
'user_unblocked_success' => 'Benutzer erfolgreich entsperrt.',
'user_blocked_success' => 'Benutzer erfolgreich blockiert.',
'block_user_fetch_success' => 'Blockierter Benutzer erfolgreich abgerufen',
'block_user_not_found' => 'Blockierter Benutzer nicht gefunden',
'user_already_reported' => 'Sie haben diesen Benutzer bereits gemeldet.',
'user_reported_success' => 'Benutzer erfolgreich gemeldet.',
'report_user_id_required' => 'Die Melde-Benutzer-ID ist erforderlich.',
'reason_required' => 'Das Feld "Grund" ist erforderlich.',
'blog_fetch_success' => 'Blogs erfolgreich abgerufen.',
'no_blog_found' => 'Keine Blogs gefunden.',
'tags_fetch_success' => 'Tags erfolgreich abgerufen.',
'blood_request_not_found' => 'Blutspendeanfrage nicht gefunden.',
'unauthenticated_error' => 'Sie sind nicht berechtigt, diese Blutspendeanfrage zu löschen.',
'blood_request_delete_success' => 'Blutspendeanfrage erfolgreich gelöscht.',
'messages_fetch_success' => 'Nachrichten erfolgreich abgerufen.',
'no_messages_found' => 'Keine Nachrichten gefunden.',
'error_occurred' => 'Ein Fehler ist aufgetreten: {0}',
'to_id_required' => 'Die Empfänger-ID ist erforderlich.',
'to_id_integer' => 'Die Empfänger-ID muss eine gültige Ganzzahl sein.',
'chat_list_fetch_success' => 'Chat-Liste erfolgreich abgerufen.',
'no_chats_found' => 'Keine Chats für die angegebene Seite gefunden.',
'page_id_required' => 'Die Seiten-ID ist erforderlich.',
'page_id_integer' => 'Die Seiten-ID muss eine gültige Ganzzahl sein.',
'message_deleted_success' => 'Die Nachricht wurde erfolgreich gelöscht.',
'not_allowed_to_delete_message' => 'Sie sind nicht berechtigt, diese Nachricht zu löschen.',
'message_not_found' => 'Nachricht nicht gefunden.',
'chat_users_fetched_success' => 'Chat-Benutzer erfolgreich abgerufen.',
'paypal_deposit_success' => 'Betrag erfolgreich über PayPal eingezahlt.',
'stripe_deposit_success' => 'Betrag erfolgreich über Stripe eingezahlt.',
'paystack_deposit_success' => 'Betrag erfolgreich über Paystack eingezahlt.',
'flutterwave_deposit_success' => 'Betrag erfolgreich über Flutterwave eingezahlt.',
'gateway_id_required' => 'Das Zahlungsgateway ist erforderlich.',
'transaction_id_required' => 'Die Transaktions-ID ist erforderlich.',
'event_created_success' => 'Veranstaltung erfolgreich erstellt.',
'name_required' => 'Der Veranstaltungsname ist erforderlich.',
'name_invalid' => 'Der Veranstaltungsname darf nur alphanumerische Zeichen und Leerzeichen enthalten.',
'location_required' => 'Der Veranstaltungsort ist erforderlich.',
'start_date_required' => 'Das Startdatum ist erforderlich.',
'start_time_required' => 'Die Startzeit ist erforderlich.',
'end_date_required' => 'Das Enddatum ist erforderlich.',
'end_time_required' => 'Die Endzeit ist erforderlich.',
'fetch_events_success' => 'Veranstaltungen erfolgreich abgerufen.',
'events_not_found' => 'Keine Veranstaltungen gefunden.',
'fetch_interested_success' => 'Interessierte Veranstaltungen erfolgreich abgerufen.',
'no_interest_events_found' => 'Keine interessierten Veranstaltungen gefunden.',
'fetch_going_success' => 'Teilnehmende Veranstaltungen erfolgreich abgerufen.',
'going_events_not_found' => 'Keine teilnehmenden Veranstaltungen gefunden.',
'fetch_my_events_success' => 'Meine Veranstaltungen erfolgreich abgerufen.',
'my_events_not_found' => 'Keine Veranstaltungen für Sie gefunden.',
'event_id_required' => 'Veranstaltungs-ID ist erforderlich.',
'interest_marked_as_not_interested' => 'Veranstaltung als nicht interessiert markiert.',
'interest_marked_as_interested' => 'Veranstaltung als interessiert markiert.',
'going_marked_as_not_going' => 'Veranstaltung als nicht teilnehmend markiert.',
'going_marked_as_going' => 'Veranstaltung als teilnehmend markiert.',
'event_update_success' => 'Veranstaltungsfelder erfolgreich aktualisiert.',
'event_update_failure' => 'Fehler beim Aktualisieren der Veranstaltung.',
'not_allowed' => 'Sie sind nicht berechtigt, diese Aktion auszuführen.',
'event_not_found' => 'Veranstaltung nicht gefunden.',
'event_deleted_success' => 'Veranstaltung erfolgreich gelöscht.',
'request_action_required' => 'Anforderungsaktion ist erforderlich.',
'friend_request_accepted' => 'Freundschaftsanfrage angenommen.',
'friend_request_declined' => 'Freundschaftsanfrage abgelehnt.',
'friend_request_not_found' => 'Freundschaftsanfrage nicht gefunden.',
'userNotFound' => 'Benutzer nicht gefunden',
'privacySettings' => 'Anfrage kann aufgrund der Datenschutzeinstellungen nicht verarbeitet werden.',
'requestCancelled' => 'Freundschaftsanfrage abgebrochen.',
'alreadyFriends' => 'Ihr seid bereits Freunde.',
'pendingRequest' => 'Sie haben eine ausstehende Freundschaftsanfrage von diesem Benutzer.',
'requestSent' => 'Freundschaftsanfrage erfolgreich gesendet.',
'validationError' => 'Validierungsfehler',
'apiKeyUnauthorized' => 'Nicht autorisiert',
'friend_two_required' => 'Das Feld "friend_two" ist erforderlich.',
'request_id_required' => 'Das Feld "request_id" ist erforderlich.',
'requestSuccessfullyDeleted' => 'Freundschaftsanfrage erfolgreich gelöscht.',
'requestNotFound' => 'Freundschaftsanfrage nicht gefunden.',
'unfriendSuccess' => 'Entfreundung erfolgreich.',
'friendRequestNotFound' => 'Freundschaftsanfrage nicht gefunden.',
'noRecommendations' => 'Keine Empfehlungen gefunden.',
'recommendationsFound' => 'Empfehlungen gefunden.',
'role_updated_success' => 'Freundrolle erfolgreich aktualisiert',
'friend_list_fetch' => 'Freundesliste erfolgreich abgerufen',
'friend_not_found' => 'Sie haben derzeit keine Freunde.',
'sent_requests_fetched' => 'Gesendete Anfragen erfolgreich abgerufen.',
'no_sent_requests' => 'Sie haben keine gesendeten Anfragen.',
'fetch_games_success' => 'Spieldaten erfolgreich abgerufen.',
'no_games_found' => 'Keine Spiele gefunden.',
'groups_fetched_successfully' => 'Gruppen erfolgreich abgerufen.',
'no_groups_found' => 'Keine Gruppen gefunden.',
'user_groups_fetched_successfully' => 'Benutzergruppen erfolgreich abgerufen.',
'no_user_groups_found' => 'Keine Benutzergruppen gefunden.',
'group_title_required' => 'Gruppentitel ist erforderlich.',
'group_title_invalid_characters' => 'Der Gruppentitel enthält ungültige Zeichen.',
'about_group_required' => 'Über die Gruppe ist erforderlich.',
'category_required' => 'Kategorie ist erforderlich.',
'privacy_required' => 'Datenschutzeinstellung ist erforderlich.',
'group_created_successfully' => 'Gruppe erfolgreich erstellt.',
'group_id_required' => 'Gruppen-ID ist erforderlich.',
'group_data_fetch_success' => 'Gruppendaten erfolgreich abgerufen.',
'group_data_not_found' => 'Gruppendaten nicht gefunden.',
'group_update_success' => 'Gruppenfelder erfolgreich aktualisiert.',
'unauthorized_access' => 'Sie sind nicht berechtigt, diese Aktion auszuführen.',
'group_not_found' => 'Gruppe nicht gefunden.',
'already_member' => 'Sie sind bereits Mitglied dieser Gruppe.',
'group_join_success' => 'Gruppe erfolgreich beigetreten.',
'not_member' => 'Sie sind kein Mitglied der Gruppe.',
'group_left_success' => 'Erfolgreich aus der Gruppe ausgetreten.',
'no_data_found' => 'Keine Daten gefunden.',
'member_already_in_group' => 'Dieses Mitglied ist bereits in der Gruppe.',
'member_added_successfully' => 'Mitglied erfolgreich hinzugefügt.',
'user_not_member' => 'Dieser Benutzer ist kein Mitglied der Gruppe.',
'member_removed_successfully' => 'Mitglied erfolgreich entfernt.',
'group_members_fetched_successfully' => 'Gruppenmitglieder erfolgreich abgerufen.',
'group_members_not_found' => 'Gruppenmitglieder nicht gefunden.',
'admin_creation_success' => 'Gruppenadministrator erfolgreich erstellt.',
'not_group_admin' => 'Dieser Benutzer ist kein Administrator der Gruppe.',
'admin_dismiss_success' => 'Gruppenadministrator erfolgreich entlassen.',
'groups_fetch_success' => 'Gruppen erfolgreich abgerufen.',
'user_groups_not_found' => 'Benutzergruppen nicht gefunden.',
'job_id_required' => 'Job-ID ist erforderlich',
'job_title_required' => 'Jobtitel ist erforderlich.',
'job_description_required' => 'Jobbeschreibung ist erforderlich.',
'job_location_required' => 'Jobstandort ist erforderlich.',
'minimum_salary_required' => 'Mindestgehalt ist erforderlich.',
'maximum_salary_required' => 'Höchstgehalt ist erforderlich.',
'currency_required' => 'Währung ist erforderlich.',
'salary_date_required' => 'Gehaltsdatum ist erforderlich.',
'experience_years_required' => 'Erfahrungsjahre sind erforderlich.',
'my_jobs_fetched_successfully' => 'Meine Jobs erfolgreich abgerufen.',
'all_jobs_fetched_successfully' => 'Alle Jobs erfolgreich abgerufen.',
'no_job_found' => 'Kein Job gefunden.',
'application_successful' => 'Bewerbung erfolgreich eingereicht.',
'already_applied' => 'Sie haben sich bereits für diesen Job beworben.',
'job_id_integer' => 'Die Job-ID muss eine Ganzzahl sein.',
'phone_required' => 'Die Telefonnummer ist erforderlich.',
'phone_invalid' => 'Die Telefonnummer ist ungültig.',
'cv_file_optional' => 'Lebenslauf-Datei ist optional.',
'cv_file_uploaded' => 'Lebenslauf-Datei muss hochgeladen werden.',
'cv_file_max_size' => 'Die Lebenslauf-Dateigröße darf 2 MB nicht überschreiten.',
'cv_file_mime_in' => 'Die Lebenslauf-Datei muss ein PDF oder Word-Dokument sein.',
'applied_successfully' => 'Erfolgreich beworben.',
'already_applied_for_job' => 'Sie haben sich bereits für diesen Job beworben.',
'search_parameters_missing' => 'Bitte geben Sie den Typ oder Titel des Jobs ein.',
'search_success' => 'Jobsuche erfolgreich.',
'no_jobs_found' => 'Kein Job gefunden.',
'job_update_success' => 'Jobfelder erfolgreich aktualisiert.',
'unauthorized' => 'Sie sind nicht berechtigt.',
'job_not_found' => 'Job nicht gefunden.',
'job_categories_fetch_success' => 'Jobkategorien erfolgreich abgerufen.',
'no_job_category_found' => 'Keine Jobkategorien gefunden.',
'fetch_applied_candidates_success' => 'Erfolgreich Bewerber abgerufen.',
'candidates_not_found' => 'Bewerber nicht gefunden.',
'fetch_notifications_success' => 'Benachrichtigungen erfolgreich abgerufen.',
'no_notifications_found' => 'Keine Benachrichtigungen gefunden.',
'notifications_list_success' => 'Benachrichtigungsliste erfolgreich abgerufen.',
'all_notifications_marked_as_read' => 'Alle Benachrichtigungen wurden als gelesen markiert.',
'all_notifications_deleted_successfully' => 'Alle Benachrichtigungen wurden erfolgreich gelöscht.',
'notification_not_found' => 'Benachrichtigung nicht gefunden',
'notification_deleted_successfully' => 'Benachrichtigung erfolgreich gelöscht',
'notification_updated_successfully' => 'Benachrichtigungsfelder erfolgreich aktualisiert.',
'job_deleted_successfully' => 'Der Job wurde erfolgreich gelöscht.',
'transaction_failed' => 'Transaktion fehlgeschlagen.',
'page_created_successfully' => 'Die Seite wurde erfolgreich erstellt',
'page_title_required' => 'Seitentitel ist erforderlich',
'page_title_min_length' => 'Der Seitentitel muss mindestens 5 Zeichen lang sein',
'page_title_max_length' => 'Der Seitentitel darf nicht länger als 50 Zeichen sein',
'page_title_invalid_characters' => 'Der Seitentitel enthält ungültige Zeichen',
'page_description_required' => 'Seitenbeschreibung ist erforderlich',
'page_category_required' => 'Seitenkategorie ist erforderlich',
'page_deleted_successfully' => 'Die Seite wurde erfolgreich gelöscht',
'page_not_found' => 'Seite nicht gefunden',
'permission_denied' => 'Sie haben keine Berechtigung, diese Seite zu aktualisieren',
'page_updated_successfully' => 'Seitenfelder erfolgreich aktualisiert',
'email_subject' => 'Gefällt Ihre Seite',
'email_body_liked_page' => 'Jemand hat Ihre Seite geliked.',
'notification_liked_page' => 'hat Ihre Seite geliked.',
'push_notification_liked_page' => 'hat Ihre Seite geliked',
'page_successfully_liked' => 'Seite erfolgreich geliked.',
'page_successfully_unliked' => 'Seite erfolgreich entliked.',
'fetch_liked_pages_success' => 'Geliked-Seiten erfolgreich abgerufen',
'no_liked_pages_found' => 'Keine geliked-Seiten gefunden',
'deleted_user_id_required' => 'Die gelöschte Benutzer-ID ist erforderlich.',
'pages_fetch_success' => 'Seiten erfolgreich abgerufen.',
'user_removed' => 'Benutzer wurde erfolgreich entfernt.',
'post_text_required' => 'Beitragstext ist erforderlich.',
'input_required' => 'Mindestens einer der Beiträge-Text, Bilder, Audio oder Video ist erforderlich.',
'not_a_group_member' => 'Sie sind kein Mitglied der Gruppe.',
'post_created_success'=>'Beitrag erfolgreich erstellt',
'post_detail'=>'Beitragsdetails',
'post_saved_list'=>'Gespeicherte Beiträge Liste',
'success'=>'Erfolg',
'post_id' => 'Beitrags-ID',
'post_id_required' => 'Die Beitrags-ID ist erforderlich.',
'validation_failed' => 'Validierung fehlgeschlagen.',
'post_not_found' => 'Beitrag existiert nicht.',
'post_detail_fetched' => 'Beitragsdetails erfolgreich abgerufen.',
'post_deleted_successfully' => 'Beitrag erfolgreich gelöscht.',
'unauthorized_to_delete_post' => 'Sie sind nicht berechtigt, diesen Beitrag zu löschen.',
'post_id_numeric' => 'Beitrags-ID muss numerisch sein.',
'ad_title' => 'Werbungstitel',
'title_required' => 'Der Titel ist erforderlich.',
'title_max_length' => 'Der Titel darf nicht länger als 150 Zeichen sein.',
'ad_link' => 'Werbungslink',
'link_required' => 'Der Link ist erforderlich.',
'link_max_length' => 'Der Link darf nicht länger als 200 Zeichen sein.',
'ad_body' => 'Werbungstext',
'body_required' => 'Der Text ist erforderlich.',
'body_max_length' => 'Der Text darf nicht länger als 250 Zeichen sein.',
'image_upload_failed' => 'Bild hochladen fehlgeschlagen.',
'advertisement_added_successfully' => 'Werbung erfolgreich hinzugefügt.',
'failed_to_add_advertisement' => 'Fehler beim Hinzufügen der Werbung.',
'comment_text' => 'Kommentartext',
'comment_text_required' => 'Kommentartext ist erforderlich.',
'comment_added_successfully' => 'Kommentar erfolgreich hinzugefügt.',
'failed_to_add_comment' => 'Fehler beim Hinzufügen des Kommentars.',
'commented_on_post' => 'hat Ihren Beitrag kommentiert.',
'post_comment' => 'Beitrag kommentieren',
'comments_fetched' => 'Kommentare erfolgreich abgerufen.',
'comments_not_found' => 'Keine Kommentare für diesen Beitrag gefunden.',
'comment_id' => 'Kommentar-ID',
'comment_id_required' => 'Kommentar-ID ist erforderlich.',
'comment_id_numeric' => 'Kommentar-ID muss numerisch sein.',
'comment_liked' => 'Kommentar erfolgreich geliked.',
'comment_unliked' => 'Sie haben den Kommentar entliked.',
'like_failed' => 'Fehler beim Liken des Kommentars.',
'new_comment_text' => 'Neuer Kommentartext',
'new_comment_text_required' => 'Kommentartext ist erforderlich.',
'new_comment_text_string' => 'Kommentartext muss eine gültige Zeichenfolge sein.',
'comment_not_found' => 'Kommentar nicht gefunden.',
'comment_update_permission_denied' => 'Sie sind nicht berechtigt, diesen Kommentar zu aktualisieren.',
'comment_updated_success' => 'Kommentar erfolgreich aktualisiert.',
'comment_update_failed' => 'Fehler beim Aktualisieren des Kommentars.',
'reply_text' => 'Antworttext',
'reply_text_required' => 'Antworttext ist erforderlich.',
'reply_text_string' => 'Antworttext muss eine gültige Zeichenfolge sein.',
'reply_added_successfully' => 'Antwort erfolgreich hinzugefügt.',
'comment_reply_id' => 'Kommentar-Antwort-ID',
'comment_reply_id_required' => 'Kommentar-Antwort-ID ist erforderlich.',
'comment_reply_id_numeric' => 'Kommentar-Antwort-ID muss numerisch sein.',
'already_liked_comment_reply' => 'Sie haben diese Kommentarantwort bereits geliked.',
'comment_reply_liked_successfully' => 'Kommentarantwort erfolgreich geliked.',
'comment_reply_like_failed' => 'Fehler beim Liken der Kommentarantwort.',
'reply_failed' => 'Fehler beim Hinzufügen der Antwort zum Kommentar.',
'post_shared_success' => 'Der Beitrag wurde erfolgreich geteilt.',
'shared_your_post' => 'hat Ihren Beitrag geteilt',
'share_post_subject' => 'Beitrag teilen',
'server_error' => 'Ein interner Serverfehler ist aufgetreten',
'comment_deleted_success' => 'Kommentar erfolgreich gelöscht.',
'post_saved_success' => 'Beitrag erfolgreich gespeichert.',
'saved_post_deleted_success' => 'Gespeicherter Beitrag erfolgreich gelöscht.',
'action' => 'Aktion',
'action_required' => 'Aktion ist erforderlich.',
'post_deleted_success' => 'Beitrag erfolgreich gelöscht.',
'unauthorized_delete' => 'Sie sind nicht berechtigt, diesen Beitrag zu löschen.',
'invalid_action' => 'Ungültige Aktion.',
'post_reported_success' => 'Beitrag erfolgreich gemeldet.',
'post_already_reported' => 'Sie haben diesen Beitrag bereits gemeldet.',
'comments_disabled_success' => 'Kommentare erfolgreich deaktiviert.',
'comments_enabled_success' => 'Kommentare erfolgreich aktiviert.',
'unauthorized_action' => 'Sie sind nicht berechtigt, diese Aktion auszuführen.',
'reaction_removed_success' => 'Reaktion erfolgreich entfernt.',
'reaction_updated_success' => 'Reaktion erfolgreich aktualisiert.',
'post_reaction_added_success' => 'Beitragsreaktion erfolgreich hinzugefügt.',
'reacted_on_your_post' => 'hat auf Ihren Beitrag reagiert.',
'post_reaction_not_found' => 'Beitragsreaktion nicht gefunden',
'shared_post_deleted' => 'Der geteilte Beitrag wurde gelöscht.',
'reply_id_required' => 'Die Antwort-ID ist erforderlich.',
'reply_deleted_success' => 'Die Kommentarantwort wurde erfolgreich gelöscht.',
'comment_required' => 'Der Kommentartext ist erforderlich.',
'comment_reply_created_success' => 'Kommentarantwort erfolgreich erstellt.',
'comment_replies_success' => 'Kommentarantworten erfolgreich abgerufen.',
'great_job_already_assigned' => 'Great Job bereits zugewiesen.',
'own_post_great_job' => 'Das ist Ihr eigener Beitrag.',
'insufficient_balance_great_job' => 'Unzureichendes Guthaben für die Vergabe von Great Job.',
'great_job_awarded_success' => 'Great Job erfolgreich vergeben.',
'insufficient_balance_coc' => 'Unzureichendes Guthaben für die Vergabe von Cup of Coffee.',
'coc_already_assigned' => 'Cup of Coffee bereits zugewiesen.',
'cannot_award_own_post_coc' => 'Sie können den Cup of Coffee nicht an Ihren eigenen Beitrag vergeben.',
'cup_of_coffee_awarded_success' => 'Cup of Coffee erfolgreich vergeben.',
'error' => 'Fehler',
'ad_not_found' => 'Anzeige nicht gefunden',
'ad_approved' => 'Ihre Anzeigenanfrage wurde genehmigt',
'ad_approve_success' => 'Anzeigenanfrage erfolgreich genehmigt',
'ad_not_approved_balance' => 'Ihre Anzeigenanfrage wurde aufgrund unzureichenden Guthabens nicht genehmigt.',
'ad_approve_fail_balance' => 'Anzeigenanfrage kann aufgrund unzureichenden Guthabens nicht genehmigt werden',
'ad_rejected' => 'Ihre Anzeigenanfrage wurde abgelehnt',
'ad_reject_success' => 'Anzeigenanfrage erfolgreich abgelehnt',
'ad_id_required' => 'Die Anzeigen-ID ist erforderlich',
'privacy_changed' => 'Beitragsdatenschutz geändert zu {privacy}',
'privacy_public' => 'Öffentlich',
'privacy_friends' => 'Freunde',
'privacy_only_me' => 'Nur Ich',
'privacy_family' => 'Familie',
'privacy_business' => 'Geschäftlich',
'post_updated' => 'Beitrag ist aktualisiert',
'advertisement_request_fetch_success' => 'Post-Werbeanfrage erfolgreich abgerufen',
'advertisement_request_not_found' => 'Post-Werbeanfrage nicht gefunden',
'status_pending' => 'Ausstehend',
'status_approved' => 'Genehmigt',
'status_rejected' => 'Abgelehnt',
'poll_id_required' => 'Die Umfrage-ID ist erforderlich',
'poll_id_integer' => 'Die Umfrage-ID muss eine Ganzzahl sein',
'poll_option_id_required' => 'Die Umfrageoption-ID ist erforderlich',
'poll_option_id_integer' => 'Die Umfrageoption-ID muss eine Ganzzahl sein',
'poll_not_found' => 'Umfrage nicht gefunden',
'poll_option_not_found' => 'Umfrageoption nicht gefunden',
'already_voted' => 'Sie haben bereits abgestimmt, Sie können nicht erneut abstimmen',
'vote_successful' => 'Erfolgreich abgestimmt',
'trending_hashtags_found' => 'Trend-Hashtags erfolgreich gefunden',
'trending_hashtags_not_exist' => 'Trend-Hashtags existieren nicht',
'amount_required' => 'Der Betrag ist erforderlich',
'cannot_feed_own_post' => 'Sie können Ihren eigenen Beitrag nicht füttern',
'product_name_required' => 'Produktname ist erforderlich',
'product_description_required' => 'Produktbeschreibung ist erforderlich',
'price_required' => 'Preis ist erforderlich',
'units_required' => 'Einheiten sind erforderlich',
'images_required' => 'Produktbilder sind erforderlich',
'images_ext_in' => 'Die Bilder müssen vom Typ sein: png, jpg, jpeg',
'images_is_image' => 'Die Datei muss ein gültiges Bild sein',
'product_added_successfully' => 'Produkt erfolgreich hinzugefügt',
'internal_server_error' => 'Interner Serverfehler',
'validation_errors' => 'Validierungsfehler',
'post_feded_successfully' => 'Beitrag erfolgreich gefüttert',
'product_not_found' => 'Produkt nicht gefunden',
'fetch_user_product_success' => 'Benutzerprodukt erfolgreich abgerufen',
'invalid_user_id' => 'Ungültige Benutzer-ID',
'product_id_required' => 'Produkt-ID ist erforderlich',
'product_updated_successfully' => 'Produktfelder erfolgreich aktualisiert',
'product_deleted_successfully' => 'Das Produkt wurde gelöscht',
'privacy_integer' => 'Die Datenschutzeinstellung muss eine Ganzzahl sein',
'description_required' => 'Die Beschreibung ist erforderlich',
'space_created_successfully' => 'Raum erfolgreich erstellt',
'space_id_required' => 'Raum-ID ist erforderlich',
'space_updated_successfully' => 'Raumfelder erfolgreich aktualisiert',
'space_not_found' => 'Raum nicht gefunden',
'space_deleted_successfully' => 'Raum erfolgreich gelöscht',
'cannot_join_own_space' => 'Sie können Ihrem eigenen Raum nicht beitreten',
'already_member_of_space' => 'Sie sind bereits Mitglied dieses Raums',
'space_joined_successfully' => 'Raum erfolgreich beigetreten',
'not_member_of_space' => 'Sie sind kein Mitglied dieses Raums',
'space_left_successfully' => 'Raum erfolgreich verlassen',
'already_cohost' => 'Sie sind bereits Co-Host dieses Raums',
'cohost_created_successfully' => 'Co-Host erfolgreich erstellt',
'user_not_member_of_space' => 'Der Benutzer ist kein Mitglied dieses Raums',
'cohost_removed_successfully' => 'Co-Host erfolgreich entfernt',
'not_a_cohost' => 'Der Benutzer ist kein Co-Host dieses Raums',
'spaces_fetched_successfully' => 'Räume erfolgreich abgerufen',
'spaces_data_fetched_successfully' => 'Räume Daten erfolgreich abgerufen',
'story_created_successfully' => 'Die Geschichte wurde erfolgreich erstellt',
'stories_fetched_successfully' => 'Die Geschichten wurden erfolgreich abgerufen',
'user_muted_successfully' => 'Der Benutzer wurde erfolgreich stummgeschaltet',
'user_unmuted_successfully' => 'Der Benutzer wurde erfolgreich wieder aktiviert',
'story_id_required' => 'Das Feld für die Story-ID ist erforderlich.',
'own_story' => 'Das ist Ihre eigene Geschichte',
'story_seen_successfully' => 'Die Geschichte wurde erfolgreich angesehen',
'story_already_seen' => 'Die Geschichte wurde bereits angesehen',
'viewed_story_notification' => 'hat Ihre Geschichte angesehen.',
'viewed_story_email_subject' => 'Angesehene Geschichte',
'viewed_story_email_body' => 'hat Ihre Geschichte angesehen',
'story_seen_user_fetch_successfully' => 'Story-Ansicht Benutzer erfolgreich abgerufen',
'no_views_found' => 'Keine Ansichten gefunden',
'story_deleted_successfully' => 'Die Geschichte wurde erfolgreich gelöscht',
'story_not_found' => 'Geschichte nicht gefunden',
'blocked_user' => 'Blockierter Benutzer',
'user_profile_fetch_successfully' => 'Benutzerprofil erfolgreich abgerufen',
'profile_not_found' => 'Profil nicht gefunden',
'viewed_your_profile' => 'hat Ihr Profil angesehen',
'view_profile_subject' => 'Profil ansehen',
'view_profile_text' => 'hat Ihr Profil angesehen',
'search_user_fetch_successfully' => 'Suchbenutzer erfolgreich abgerufen',
'search_group_fetch_successfully' => 'Suchgruppe erfolgreich abgerufen',
'search_events_successfully' => 'Veranstaltungen erfolgreich durchsucht',
'search_jobs_successfully' => 'Jobs erfolgreich durchsucht',
'package_not_exist' => 'Paket existiert nicht',
'already_subscribed' => 'Sie sind bereits für ein höheres oder gleichwertiges Paket abonniert.',
'subscription_success' => 'Paketabonnement erfolgreich.',
'package_id_label' => 'Paket-ID',
'package_id_required' => 'Paket-ID ist erforderlich.',
'package_upgrade_not_allowed' => 'Wir können dieses Paket nicht upgraden, da Ihr höheres Abonnement bereits aktiv ist.',
'package_subscription_success' => 'Paketabonnement erfolgreich.',
'package_already_subscribed' => 'Paket bereits abonniert.',
'account_deletion_not_available' => 'Kontolöschung ist nicht verfügbar.',
'incorrect_password' => 'Falsches Passwort.',
'account_deleted_successfully' => 'Konto erfolgreich gelöscht.',
'account_deletion_failed' => 'Kontolöschung fehlgeschlagen',
'user_fetch_successfully' => 'Benutzer erfolgreich abgerufen',
'pro_user_not_found' => 'Pro Benutzer nicht gefunden.',
'poke_successfully' => 'Anstupsen erfolgreich',
'poked_you' =>'hat Sie angestupst',
'blood_donor_found' => 'Blutspender erfolgreich gefunden',
'blood_donor_not_found' => 'Blutspender nicht gefunden',
'blood_request_added_successfully' => 'Blutspendeanfrage erfolgreich hinzugefügt',
'blood_group_required' => 'Blutgruppe ist erforderlich.',
'cannot_transfer_to_self' => 'Kann nicht auf Ihr eigenes Konto übertragen werden',
'amount_transferred_successfully' => 'Betrag erfolgreich übertragen',
'transfer_failed_due_to' => 'Betrag kann nicht übertragen werden aufgrund: ',
'fund_id_required' => 'Mittel-ID ist erforderlich',
'donation_not_found' => 'Spende nicht gefunden',
'donation_successful' => 'Erfolgreich gespendet',
'donation_failed_due_to' => 'Betrag kann nicht übertragen werden aufgrund: ',
'profile_updated_successfully'=>'Profil erfolgreich aktualisiert',
'admin_withdraw_error'=>'Admin kann keine Auszahlung erstellen',
'paypal_withdraw_success'=>'Auszahlung über Paypal erfolgreich erstellt',
'bank_withdraw_success'=>'Auszahlung über die Bank erfolgreich erstellt',
'job_created_successfully' => 'Job erfolgreich erstellt.',
'moduleid_required' => 'Modul-ID ist erforderlich.',
'modulename_required' => 'Modulname ist erforderlich.',
'already_reported' => 'Sie haben dieses Element bereits gemeldet.',
'cannot_report_your_own_product' => 'Sie können Ihr eigenes Produkt nicht melden.',
'cannot_report_your_own_event' => 'Sie können Ihr eigenes Ereignis nicht melden.',
'cannot_report_your_own_page' => 'Sie können Ihre eigene Seite nicht melden.',
'cannot_report_your_own_group' => 'Sie können Ihre eigene Gruppe nicht melden.',
'reported_successfully' => 'Erfolgreich gemeldet.',
'module_not_found' => 'Modul nicht gefunden.',
'cannot_report_your_own_job' => 'Du kannst deinen eigenen Job nicht melden.',
'cannot_report_your_own_space' => 'Du kannst deinen eigenen Bereich nicht melden.',
'is_paid_required' => '"Bezahlt" ist erforderlich.',
'course_price_not_zero' => 'Der Kurspreis darf nicht null sein.',
'id_required' => 'ID ist erforderlich',
'format_required' => 'Format ist erforderlich',
'type_error' => 'Typ nicht erkannt',
'not_donation_post' => 'Dieser Beitrag ist kein Spendenbeitrag.',
'funding_not_found' => 'Finanzierungsdetails nicht gefunden.',
'funding_list_fetch_success' => 'Finanzierungsdetails erfolgreich abgerufen.',
'lang_not_found' => 'Sprache nicht gefunden',
'translate_failed' => 'Text konnte nicht übersetzt werden',
'fetch_pokes_success' => 'Pokes erfolgreich abgerufen!!!',
'poke_not_found' => 'Keine Poke-Historie gefunden',
'movie_id_required' => 'Film-ID ist erforderlich.',
'movie_not_found' => 'Der angeforderte Film wurde nicht gefunden.',
'movie_found_success' => 'Filmdetails erfolgreich abgerufen.',
'recipient_id' => 'Empfänger-ID',
'tag_id_required' => 'Tag-ID ist erforderlich.',
'blog_not_found' => 'Keine Blogs für diesen Tag gefunden.',
'blog_id_required' => 'Blog-ID ist erforderlich.',
'facilities_fetch_success' => 'Einrichtungen erfolgreich abgerufen.',
'facilities_fetch_failed' => 'Fehler beim Abrufen der Einrichtungen.',
'business_categories_fetch_success' => 'Geschäftskategorien erfolgreich abgerufen.',
'business_categories_fetch_failed' => 'Fehler beim Abrufen der Geschäftskategorien.',

];
