<?php 
return [
    'all_pages' => 'All Pages',
    'no_pages_found' => 'No pages found',
    'click_here_to_add' => 'Click here to add',
    'create_page' => 'Create page',
    'likes_count' => ' Likes',
    'posts' => 'Posts',
    'newest' => 'Newest',
    'alphabetical' => 'Alphabetical',
    'suggested_pages' => 'Suggested Pages',
    'my_pages' => 'My Pages',
    'create_page_title' => 'Create a page',
    'page_name' => 'Page name',
    'page_name_placeholder' => 'Page name (Required)',
    'page_name_description' => 'Name that describes what the page is about.',
    'category' => 'Category',
    'required' => 'required',
    'select_category' => 'Select Category',
    'category_required' => 'Please select a category.',
    'website_url' => 'Website URL',
    'avatar' => 'Avatar',
    'post_reaction'=>'Post Reaction',
    'no_data_found_for_reaction' => 'No data found for this reaction.', // English
    'cover' => 'Cover',
    'about_page' => 'About page',
    'description_placeholder' => 'Description (Required)',
    'character_limit' => 'Character limit: 500',
    'create_page_button' => 'Create a page',
    'page_create_status' => 'Page Create Status',
    'page_name_required' => 'Page name is required.',
    'page_name_min' => 'Page name must be at least 5 characters.',
    'page_name_max' => 'Page name cannot exceed 50 characters.',
    'description_required' => 'Page description is required.',
    'description_max' => 'Page description cannot exceed 500 characters.',
    'invalid_avatar' => 'Please select a valid avatar image (jpg, png, or gif).',
    'invalid_cover' => 'Please select a valid cover image (jpg, png, or gif).',
    'error_message' => 'An error occurred while creating the page.',
    'likes' => 'Likes',
    'edit_page' => 'Edit Page',
    'delete' => 'Delete',
    'delete_confirmation' => 'Are you sure to delete ?',
    'yes' => 'Yes!',
    'sending_request' => 'Sending request',
    'send_request' => 'Send Request',
    'update_page_title' => 'Update Page',
    'website_placeholder' => 'https://www.example.com/',
    'update_page_button' => 'Update page',
    'page_update_status' => 'Page Update Status',
    'all_groups' => 'All Groups',
    'my_groups' => 'My Groups',
    'new_group' => 'New Group',
    'create_group' => 'Create Group',
    'members' => 'Members',
    'joined' => 'Joined',
    'join' => 'Join',
    'no_groups_found' => 'No groups found',
    'are_you_sure' => 'Are you sure?',
    'edit' => 'Edit',
    'confirm_delete' => 'Are you sure to delete this record?',
    'success' => 'Success',
    'create_group_title' => 'Create a Group',
    'group_title' => 'Group Title',
    'group_name_placeholder' => 'Group name (Required)',
    'group_name_description' => 'Name that describes what the group is about.',
    'privacy' => 'Privacy',
    'public' => 'Public',
    'private' => 'Private',
    'group_profile' => 'Group profile',
    'group_cover' => 'Group Cover',
    'about_group' => 'About the group',
    'create_group_button' => 'Create Group',
    'group_title_required' => 'Please enter a group name',
    'group_title_min' => 'Group name must be at least 5 characters long',
    'group_title_max' => 'Group name must not exceed 50 characters',
    'invalid_file_extension' => 'Please select a valid file with extension',
    'error' => 'Error',
    'edit_group_title' => 'Edit Group',
    'privacy_required' => 'Please select the privacy setting',
    'error_update_group_message' => 'An error occurred while updating the group.',
    'update_group_button' => 'Update Group',
    'cancel'=>'Cancel',
    'confirm_delete_group' => 'Are you sure to delete this group?',
    'movies_title' => 'Movies',
    'discover_new_movies' => 'Discover new movies',
    'search_movies' => 'Search for movies',
    'search' => 'Search',
    'movies' => 'Movies',
    'stars' => 'Stars',
    'producer' => 'Producer',
    'release_year' => 'Release Year',
    'duration' => 'Duration',
    'minutes' => 'mins',
    'no_movies_found' => 'No movies found',
    'back_to_movies' => 'Back to movies',
    'movie_pagination' => 'Movie Pagination',
    'events' => 'Events',
    'my_events' => 'My Events',
    'create_event' => 'Create Event',
    'start_date' => 'Start Date',
    'start_time' => 'Start Time',
    'end_date' => 'End Date',
    'end_time' => 'End Time',
    'going' => 'Going',
    'not_going' => 'Not Going',
    'interested' => 'Interested',
    'not_interested' => 'Not Interested',
    'no_events_found' => 'No events found',
    'details' => 'Details',
    'event_pagination' => 'Event Pagination',
    'cannot_revert' => "You won't be able to revert this!",
    'yes_delete' => 'Yes, delete it!',
    'event_name' => 'Event Name',
    'event_name_placeholder' => 'Event Name (Required)',
    'location' => 'Location',
    'location_placeholder' => 'Location (Required)',
    'description' => 'Description',
    'cover_image' => 'Cover Image',
    'error_occurred' => 'An error occurred',
    'start_date_time_before_current' => 'Start date/time should not be before the current date/time',
    'end_date_time_before_start' => 'End date/time cannot be before start date/time',
    'end_time_before_or_equal_start' => 'End time cannot be before or equal to start time on the same day',
    'edit_event' => 'Edit Event',
    'current_cover' => 'Current Cover',
    'view_current_cover' => 'View current cover',
    'past_event_error' => 'Cannot update a past event',
    'ongoing_event_error' => 'Ongoing events cannot be updated',
    'update_event' => 'Update Event',
    'event_details' => 'Event Details',
    'event_detail' => 'Event Detail',
    'event_start_time' => 'Event Start Time',
    'event_end_time' => 'Event End Time',
    'event_description' => 'Event Description',
    'name' => 'Name',
    'email' => 'Email',
    'gender' => 'Gender',
    'profile_image' => 'Profile Image',
    'no_interested_users' => 'No interested users yet',
    'no_going_users' => 'No going users found.',
    'jobs' => 'Jobs',
    'my_jobs' => 'My Jobs',
    'sort' => 'Sort',
    'latest' => 'Latest',
    'salary_high_to_low' => 'Salary High to Low',
    'salary_low_to_high' => 'Salary Low to High',
    'distance_km' => 'Distance (KM)',
    'filter' => 'Filter',
    'applied' => 'Applied',
    'apply_now' => 'Apply Now',
    'created_at' => 'Created',
    'no_jobs_found' => 'No Jobs found',
    'create_job' => 'Create a Job',
    'job_title' => 'Job Title',
    'job_title_required' => 'Job Title (Required)',
    'job_description' => 'Job Description',
    'job_description_required' => 'Job Description (Required)',
    'job_location' => 'Job Location',
    'job_location_required' => 'Job Location (Required)',
    'salary_date' => 'Salary Date',
    'currency' => 'Currency',
    'minimum_salary' => 'Minimum Salary',
    'maximum_salary' => 'Maximum Salary',
    'job_type' => 'Job Type',
    'select_job_type' => 'Select Job Type',
    'full_time' => 'Full Time',
    'part_time' => 'Part Time',
    'internship' => 'Internship',
    'volunteer' => 'Volunteer',
    'contract' => 'Contract',
    'company_name' => 'Company Name',
    'urgent_hiring' => 'Urgent Hiring',
    'experience_years' => 'Experience Years',
    'experience_years_placeholder' => 'Experience Years',
    'active' => 'Active',
    'create_job_button' => 'Create a Job',
    'validation_job_title_required' => 'Please enter the job title',
    'validation_job_title_minlength' => 'Job title must be at least 5 characters',
    'validation_job_title_maxlength' => 'Job title cannot exceed 50 characters',
    'validation_job_description_required' => 'Please enter the job description',
    'validation_job_location_required' => 'Please enter the job location',
    'validation_salary_date_required' => 'Please select a salary date',
    'validation_currency_required' => 'Please select a currency',
    'validation_minimum_salary_required' => 'Please enter the minimum salary',
    'validation_maximum_salary_required' => 'Please enter the maximum salary',
    'validation_job_type_required' => 'Please select a job type',
    'validation_category_required' => 'Please select a category',
    'validation_company_name_required' => 'Please enter the company name',
    'validation_experience_years_required' => 'Please enter experience years',
    'validation_experience_years_number' => 'Experience years must be a number',
    'validation_experience_years_maxlength' => 'Experience years must be no more than 2 digits',
    'job_created_successfully' => 'Job created successfully!',
    'job_creation_error' => 'An error occurred while creating the job.',
    'month' => 'Month',
    'week' => 'Week',
    'hour' => 'Hour',
    'year' => 'Year',
    'day' => 'Day',
    'edit_job' => 'Edit Job',
    'job_updated_successfully' => 'Job updated successfully!',
    'job_update_error' => 'An error occurred while updating the job.',
    'discover_jobs' => 'Discover new Jobs around you.',
    'search_jobs' => 'Search for Jobs',
    'phone_number' => 'Phone No',
    'phone_number_placeholder' => 'Phone No',
    'position' => 'Position',
    'position_placeholder' => 'Position',
    'company_name_placeholder' => 'Company Name',
    'cv_file' => 'CV File',
    'address' => 'Address',
    'address_placeholder' => 'Address',
    'submit' => 'Submit',
    'file_extension_error' => 'Please select a file with extension .pdf, .doc, or .docx.',
    'job_applicants' => 'Job Applicants',
    'sort_latest' => 'Latest',
    'cv' => 'CV',
    'download' => 'Download',
    'cv_not_exist' => 'CV not Exist',
    'no_applicants_found' => 'No Applicants found',
    'list_all_jobs' => 'List all Jobs',
    'type' => 'Type',
    'status' => 'Status',
    'open' => 'Open',
    'closed' => 'Closed',
    'salary' => 'Salary',
    'experience_required' => 'Experience Required',
    'application_deadline' => 'Application Deadline',
    'already_applied' => 'Already Applied',
    'total_balance' => 'Total Balance',
    'stripe' => 'Stripe',
    'paypal' => 'Paypal',
    'paystack' => 'Paystack',
    'withdraw' => 'Withdraw',
    'withdraw_requests' => 'Withdrawal Requests',
    'transfer' => 'Transfer',
    'earning_breakdown' => 'Earning Breakdown',
    'like_earnings' => 'Like Earnings',
    'comment_earnings' => 'Comment Earnings',
    'share_earnings' => 'Share Earnings',
    'withdraw_earnings' => 'Withdraw Earnings',
    'deposit_earnings' => 'Deposit Earnings',
    'package_subscription_earnings' => 'Package Subscription Earnings',
    'coffee_earnings' => 'Cup of Coffee Earnings',
    'great_job_earnings' => 'Great Job Earnings',
    'withdraw_rejected_earnings' => 'Withdraw Rejected Earnings',
    'advertisement_earnings' => 'Advertisement Earnings',
    'deposit_via_stripe' => 'Deposit Via Stripe',
    'card_info' => 'Card Information',
    'amount' => 'Amount',
    'enter_deposit' => 'Enter Deposit',
    'pay' => 'Pay',
    'deposit_via_paypal' => 'Deposit Via Paypal',
    'deposit_via_paystack' => 'Deposit Via Paystack',
    'withdraw_balance' => 'Withdraw Balance',
    'withdraw_via' => 'Withdraw Request Via',
    'select_withdrawal_method' => 'Request Via',
    'withdrawal_amount' => 'Withdrawal Amount',
    'paypal_email' => 'PayPal Email',
    'enter_paypal_email' => 'Enter PayPal Email',
    'full_name' => 'Full Name',
    'enter_full_name' => 'Your Full Name',
    'enter_address' => 'Enter Address',
    'iban' => 'IBAN',
    'enter_iban' => 'Your IBAN',
    'country' => 'Country',
    'enter_country' => 'Your Country',
    'swift_code' => 'SWIFT Code',
    'enter_swift_code' => 'Your SWIFT Code',
    'mobile_number' => 'Mobile Number',
    'enter_mobile_number' => 'Your Mobile Number',
    'amount_required' => 'Please enter an amount.',
    'valid_number' => 'Please enter a valid number.',
    'withdrawal_method_required' => 'Please select a withdrawal method.',
    'paypal_email_required' => 'Please enter your PayPal email address.',
    'valid_email' => 'Please enter a valid email address.',
    'full_name_required' => 'Please enter your full name.',
    'iban_required' => 'Please enter your IBAN.',
    'country_required' => 'Please enter your country.',
    'swift_code_required' => 'Please enter your SWIFT code.',
    'address_required' => 'Please enter your address.',
    'mobile_number_required' => 'Please enter your mobile number.',
    'withdraw_history' => 'Withdraw History',
    'sr' => 'Sr',
    'request_via' => 'Request Via',
    'pending' => 'Pending',
    'approved' => 'Approved',
    'rejected' => 'Rejected',
    'no_withdraw_requests' => 'No withdraw requests found',
    'transfer_amount' => 'Transfer Amount',
    'user_name' => 'User Name',
    'please_enter_amount' => 'Please enter an amount.',
    'enter_valid_number' => 'Please enter a valid number.',
    'please_select_user' => 'Please select a user.',
    'select_valid_user' => 'Please select a valid user.',
    'friends' => 'Friends',
    'friend_requests' => 'Friend Requests',
    'suggestions' => 'Suggestions',
    'sent_requests' => 'Sent Requests',
    'no_friends' => 'No Friends',
    'mutual_friends' => 'Mutual Friendsa',
    'unfriend' => 'Unfriend',
    'cancel_request' => 'Cancel Request',
    'add_friend' => 'Add Friend',
    'accept' => 'Accept',
    'canceling_request' => 'Canceling request',
    'confirm_unfriend' => 'Are you sure to unfriend this user?',
    'family' => 'Family',
    'business' => 'Business',
    'accepting_request' => 'Accepting request',
    'deleting_request' => 'Deleting request',
    'friend_request_status' => 'Friend Request Status',
    'unfriend_confirmation' => 'Are you sure you want to unfriend this user?',
    'any' => 'Any',
    'male' => 'Male',
    'female' => 'Female',
    'relationship' => 'Relationship',
    'single' => 'Single',
    'married' => 'Married',
    'engaged' => 'Engaged',
    'pick_your_plan' => 'Pick your Plan',
    'pro_features_description' => 'Pro features give you complete control over your profile.',
    'featured_member' => 'Feature Members',
    'verified_badge' => 'Verified Badge',
    'page_promotion' => 'Page Promotion',
    'post_promotion' => 'Post Promotion',
    'edit_post' => 'Edit Post',
    'point_spendable' => 'Point Spendable',
    'selected' => 'Selected',
    'select' => 'Select',
    'confirmation_title' => 'Are you sure?',
    'confirmation_text' => 'Are you sure to purchase this package?',
    'blogs' => 'Blogs',
    'read_more' => 'Read more...',
    'blogs_not_exist' => 'No blogs available',
    'recent_post' => 'Recent Post',
    'tags' => 'Tags',
    'blog_not_found' => 'Blog not found',
    'curated_goods' => 'Curated Goods for Every Need',
    'prime_picks' => 'Prime Picks - Your Ultimate Destination for Quality Products',
    'start_search' => 'Start your search',
    'featured_products' => 'Discover featured products',
    'handpicked_items' => 'Every week we hand-pick some of the best items from our collection',
    'add_new_product' => 'Add New Product',
    'my_products' => 'My Products',
    'previous' => 'Previous',
    'next' => 'Next',
    'recent_products' => 'The Most Recent Products',
    'by' => 'by',
    'in' => 'in',
    'sales' => 'Sales',
    'no_products_category' => 'No Products Found in this category',
    'no_products' => 'No Products Found',
    'contact_seller' => 'Contact Seller',
    'product_not_found' => 'Product not found',
    'over' => 'Over',
    'curated' => 'curated',
    'design_website_templates' => 'Design & Website templates',
    'high_quality_items' => 'High quality items created by our global community',
    'discover_featured_products' => 'Discover featured products',
    'add_product' => 'Add a Product',
    'product_name' => 'Product Name',
    'product_name_placeholder' => 'Product Name (Required)',
    'product_description' => 'Product Description',
    'product_description_placeholder' => 'Product Description',
    'product_images' => 'Product Images',
    'multiple_select' => '(Hold Ctrl to select Multiple)',
    'price' => 'Price',
    'price_placeholder' => 'Price',
    'select_currency' => 'Select Currency',
    'units' => 'Units',
    'units_placeholder' => 'Units',
    'new' => 'New',
    'used' => 'Used',
    'edit_product' => 'Edit Product',
    'your_image' => 'Your Image',
    'update_product' => 'Update Product',
    'validation_required' => 'This field is required.',
    'validation_number' => 'Please enter a valid number.',
    'validation_image_accept' => 'Please upload a valid image (JPEG/PNG).',
    'confirm_remove_image' => 'Are you sure you want to remove this image?',
    'cannot_delete_all_images' => 'You cannot delete all images of the product.',
    'product_updated_successfully' => 'Product updated successfully!',
    'become_donor' => 'Become a Donor',
    'find_donor' => 'Find Donor',
    'blood_request' => 'Blood Request',
    'blood_image_alt' => 'Blood Donation Placeholder Image',
    'plus_circle_icon' => 'Plus Circle Icon',

    'blood_group' => 'Blood Group',
    'phone' => 'Phone Number',
    'donation_date' => 'Last Donation Date',
    'donation_available' => 'Available to donate blood',
    'required_blood_group' => 'Please select a blood group.',
    'required_location' => 'Please enter your address.',
    'required_phone' => 'Please enter your phone number.',
    'required_donation_date' => 'Please select a donation date.',
    'valid_donation_date' => 'Please enter a valid date.',
    'update_success' => 'Blood Information updated successfully',
    'update_error' => 'An error occurred while updating blood donation information.',

    'advertisements' => 'Advertisements',
    'sr_no' => 'Sr #',
    'image' => 'Image',
    'ad_link' => 'Ad Link',
    'ad_title' => 'Ad Title',
    'action' => 'Action',
    'no_ad_found' => 'No Ad found',
    'view_post' => 'View Post',
    'approve_ad' => 'Approve',
    'reject_ad' => 'Reject',
    'success_msg' => 'Success',
    'error_msg' => 'An error occurred.',
    'confirm_approve' => 'Are you sure to approve this ad request?',
    'confirm_reject' => 'Are you sure to reject this ad request?',
    'post_details' => 'Post Details',
    'ad_image' => 'Advertisement Image',
    'ad_body' => 'Advertisement Body',
    'approve' => 'Approve',
    'reject' => 'Reject',
    'course_applicants' => 'Course Applicants',
    'create_story' => 'Create a Story',
    'find_advertisements' => 'Find Advertisements',
    'status_pending' => 'Pending',
    'status_approved' => 'Approved',
    'status_rejected' => 'Rejected',
    'are_you_sure_approve' => 'Are you sure to approve this ad request?',
    'are_you_sure_reject' => 'Are you sure to reject this ad request?',
    'advertisements_status' => 'Advertisement Status',
    'add_new_story' => 'Add New Story',
    'my_stories' => 'My Stories',
    'choose_your_media' => 'Choose Your Media',
    'story_caption' => 'Story Caption',
    'add_story_button' => 'Add Story',
    'media' => 'Media',
    'caption' => 'Caption',
    'delete_story' => 'Delete Story',
    'invalid_upload' => 'Invalid Upload',
    'select_image_or_video' => 'Please select image or video file',
    'video_too_long' => 'Whoops! Your video is too long! 🕒 Keep it under 30 seconds, please.',
    'media_required' => 'Please select a media file',
    'are_you_sure_delete_story' => 'Are you sure to delete this story?',
    'success_story_added' => 'Story added successfully!',
    'error_adding_story' => 'An error occurred while adding the story.',
    'story_deleted_success' => 'Story deleted successfully!',
    'error_deleting_story' => 'An error occurred while deleting the story.',
    'edit_profile' => 'Edit Profile',
    'request_sent' => 'Request Sent',
    'message' => 'Message',
    'joined_on' => 'Joined On',
    'about' => 'About',
    'photos' => 'Photos',
    'videos' => 'Videos',
    'this_account_is_private' => 'This Account is Private',
    'dob' => 'DOB',
    'in_a_relationship' => 'In a Relationship',
    'none' => 'None',
    'unknown' => 'Unknown',
    'social_links' => 'Social Links',
    'see_all_photos' => 'See all photos',
    'see_all_friends' => 'See all friends',
    'report_user' => 'Report User',
    'reason' => 'Reason',
    'close' => 'Close',
    'submit_report' => 'Submit Report',
    'poke_status' => 'Poke Status',
    'user' => 'User ',
    'all_courses' => 'All Courses',
    'reason_required' => 'The reason field is required.',
    'reason_minlength' => 'The reason must be at least 5 characters long.',
    'reason_maxlength' => 'The reason must not exceed 50 characters.',
    'report_status' => 'Report Status',
    'block_status'=>'Block Status',
    'general_settings' => 'General Settings',
    'profile_avatar' => 'Profile Avatar',
    'avatar_preview' => 'Avatar Preview',
    'profile_cover' => 'Profile Cover',
    'cover_preview' => 'Cover Preview',
    'first_name' => 'First Name',
    'last_name' => 'Last Name',
    'about_you' => 'About You',
   
    'other' => 'Other',
    'city' => 'City',
    'relationship_status' => 'Relationship Status',
    'working' => 'Working',
    'occupation' => 'Your occupation',
    'save_changes' => 'Save Changes',

    // Validation messages
    'first_name_required' => 'Please enter your first name.',
    'first_name_minlength' => 'Your first name must be at least 2 characters long.',
    'first_name_maxlength' => 'Your first name cannot exceed 50 characters.',
    'last_name_required' => 'Please enter your last name.',
    'last_name_minlength' => 'Your last name must be at least 2 characters long.',
    'last_name_maxlength' => 'Your last name cannot exceed 50 characters.',
    'about_you_maxlength' => 'About you cannot exceed 300 characters.',
    'gender_required' => 'Please select your gender.',
    'relation_required' => 'Please select your relationship status.',

    // AJAX response messages
    'profile_updated_success' => 'Profile updated successfully!',
    'error_message_update' => 'An error occurred while updating the profile.',
    'update_status' => 'Update Status',
    'social_settings' => 'Social Settings',
    'facebook' => 'Facebook',
    'twitter' => 'Twitter',
    'instagram' => 'Instagram',
    'linkedin' => 'LinkedIn',
    'youtube' => 'YouTube',
    
    // Placeholders for social media inputs
    'facebook_placeholder' => 'https://www.facebook.com/',
    'twitter_placeholder' => 'https://www.twitter.com/',
    'instagram_placeholder' => 'https://www.instagram.com/',
    'linkedin_placeholder' => 'https://www.linkedin.com/',
    'youtube_placeholder' => 'https://www.youtube.com/',

    // Success and error messages
    'social_update_success' => 'Social settings updated successfully!',
    'social_update_error' => 'An error occurred while updating social settings.',
    


    'notification_settings' => 'Notification Settings',
    'liked_my_posts' => 'Liked My Posts',
    'liked_my_posts_description' => 'Get notifications when someone likes your post',
    'commented_on_my_posts' => 'Commented on My Posts',
    'commented_on_my_posts_description' => 'Get notifications for comments on your posts',
    'shared_my_posts' => 'Shared My Posts',
    'shared_my_posts_description' => 'Be notified when someone shares your posts',
    'accepted_request' => 'Accepted Friend/Follow Request',
    'accepted_request_description' => 'Know when your friend/follow request is accepted',
    'liked_my_pages' => 'Liked My Pages',
    'liked_my_pages_description' => 'Get notified when someone likes your pages',
    'joined_my_groups' => 'Joined My Groups',
    'joined_my_groups_description' => 'Find out when someone joins your groups',
    'received_message' => 'Received Message',
    'received_message_description' => 'Receive notifications for new messages',
    'friends_new_post' => 'Friends New Post',
    'friends_new_post_description' => 'Receive notifications for new posts by friends',
    'profile_visit' => 'Profile Visit',
    'profile_visit_description' => 'Receive notifications when someone visits your profile',

    // Buttons and Alerts
    'notification_update_success' => 'Notification settings updated successfully!',
    'notification_update_error' => 'An error occurred while updating notification settings.',


    'privacy_settings' => 'Privacy Settings',
    'who_can_send_friend_request' => 'Who can send friend request?',
    'friend_request_description' => 'Control who can send you friend requests.',
    'who_can_message_me' => 'Who can message me?',
    'message_me_description' => 'Set who can send you private messages.',
    'who_can_view_email' => 'Who can view my Email Address?',
    'email_view_description' => 'Set who can view your email address.',
    'who_can_view_phone' => 'Who can view my Phone?',
    'phone_view_description' => 'Set who can view your phone number.',
    'who_can_see_friends' => 'Who can see my friends?',
    'friends_view_description' => 'Choose who can see your friend list.',
    'who_can_see_birthday' => 'Who can see my birthday?',
    'birthday_view_description' => 'Control who can view your birthday.',
    'everyone' => 'Everyone',
    'no_one' => 'No one',
    'privacy_update_success' => 'Privacy settings updated successfully!',
    'privacy_update_error' => 'An error occurred while updating privacy settings.',


    'blocked_user_title' => 'Blocked User',
    'unblock' => 'Unblock',
    'no_blocked_user' => 'You currently have no blocked users.',
    'unblock_user_confirm_title' => 'Are you sure to unblock this user?',
    'unblock_user_confirm_text' => '',
    'yes_unblock' => 'Yes, unblock',
    'success_title' => 'Success',


    'manage_sessions' => 'Manage Sessions',
    'web_browser' => 'Web Browser',
    'operating_system' => 'Operating System',
    'session_id' => 'Session ID',
    'current_session' => 'You',
    'session_not_found' => 'Session not found.',
    'delete_session_confirm_title' => 'Are you sure to delete this session?',
    'delete_session_confirm_text' => '',

    'change_password' => 'Change your password',
    'current_password' => 'Current password',
    'new_password' => 'New password',
    'confirm_password' => 'Confirm password',
    'enter_old_password' => 'Enter Old Password',
    'enter_new_password' => 'Enter New Password',
    'confirm_new_password' => 'Confirm New Password',
    'update_password' => 'Update password',
    'error_title' => 'Error',
    'update_password_error' => 'An error occurred while updating the password.',
    'old_password_required' => 'Current password is required.',
    'new_password_required' => 'New password is required.',
    'confirm_password_required' => 'Please confirm your new password.',
    'password_mismatch' => 'The passwords do not match.',
    'settings' => 'Settings',
    'blocked_users' => 'Blocked Users',
    'password' => 'Password',
    'delete_account' => 'Delete Account',
    'view_profile' => 'View Profile',
    'about_us' => 'About Us',
    'support' => 'Support',
    'terms_and_conditions' => 'Terms & Conditions',
    'privacy_policy' => 'Privacy Policy',
    'page_profile_info' => 'Page Profile Info',
    'overview' => 'Overview',
    'followers_count' => 'Followers Count',
    'created_on' => 'Created on',

    'find_a_job' => 'Find a Job you',
    'love' => 'Love',
    'need' => 'Need',
    'enjoy' => 'Enjoy',
    'find_jobs_opportunities' => 'Find Jobs, Employment & Career Opportunities',
    'search_keyword_placeholder' => 'Keyword e.g. (Job Title, Description, Tags)',
    
    'liked' => 'Liked',
    'like' => 'Like',
    'reply' => 'Reply',
    'replies' => 'Replies',



    'create_post' => 'Create Post',
    'only_me' => 'Only me',
    'share_thoughts' => 'Share your thoughts...',
    'photo' => 'Photo',
    'video' => 'Video',
    'audio' => 'Audio',
    'event' => 'Event',
    'poll' => 'Poll',
    'raise_funding' => 'Raise funding',
    'post' => 'Post',
    'create_poll' => 'Create Poll',
    'question' => 'Question',
    'option' => 'Option',
    'common_interest' => 'Common Interest',
    'donation_title' => 'Donation Title',
    'donation_image' => 'Donation Image',
    'donation_amount' => 'Donation Amount',
    'donation_description' => 'Donation Description',

    'good_morning' => 'Good Morning',
    'good_afternoon' => 'Good Afternoon',
    'good_evening' => 'Good Evening',
    'good_night' => 'Good Night',
    'morning_message_1' => 'Rise and shine! A new day awaits.',
    'morning_message_2' => 'Good Morning! Embrace the day with positivity.',
    'morning_message_3' => 'Morning has come, a day full of opportunities!',
    'morning_message_4' => 'Wake up and welcome another wonderful morning in your life.',
    'morning_message_5' => 'Sunrise is a reminder that we can start new beginnings all over again.',
    'morning_message_6' => 'Good Morning! Start your day with enthusiasm!',
    'afternoon_message_1' => 'Good Afternoon! Keep up the good work.',
    'afternoon_message_2' => 'A peaceful noon is a perfect time to reflect and dream.',
    'afternoon_message_3' => 'May your afternoon be light, bright, and insightful.',
    'afternoon_message_4' => 'Good Afternoon! Take a deep breath, and keep moving forward.',
    'afternoon_message_5' => 'Half the day is over; keep it up!',
    'evening_message_1' => 'Good Evening! Relax and unwind, you’ve earned it.',
    'evening_message_2' => 'Evening is a time to pause and be grateful.',
    'evening_message_3' => 'The evening is here; time to reflect on the day.',
    'evening_message_4' => 'Enjoy the tranquility of the evening.',
    'evening_message_5' => 'Good Evening! Let the stars light up your dreams.',
    'night_message_1' => 'Good Night! Rest well and recharge for a new day.',
    'night_message_2' => 'May your dreams be sweet and your rest peaceful.',
    'night_message_3' => 'Embrace the silence of the night and find peace.',
    'night_message_4' => 'Sleep tight and get ready for a bright tomorrow.',
    'night_message_5' => 'Close your eyes, let the night take over and heal you.',
    'job_categories' => 'Job Categories',
    'all_jobs' => 'All',
    'admin_panel' => 'Admin Panel',
    'news_feed' => 'News feed',
    'pages' => 'Pages',
    'group' => 'Group',
    'games' => 'Games',
    'wallet' => 'Wallet',
    'packages' => 'Packages',
    'blog_articles' => 'Blog/Articles',
    'marketplace' => 'MarketPlace',
    'blood_bank' => 'Blood',
    'post_ads_requests' => 'Post Ads',
    'saved_posts' => 'Saved Posts',
    'explore' => 'Explore',
    'logout' => 'Logout',
    'site_name' => 'Site Name', 


    'with' => 'with',
    'is_in' => 'is in',
    'updated_profile_picture' => 'updated {gender} profile picture.',
    'updated_cover_photo' => 'updated {gender} cover photo.',
    'shared_post' => 'shared a post',
    'public_post' => 'Public Post',
    'save_post' => 'Save post',
    'unsave_post' => 'Unsave post',
    'disable_comments' => 'Disable Comments',
    'enable_comments' => 'Enable Comments',
    'delete_post' => 'Delete post',
    'report_post' => 'Report post',
    'open_in_new_tab' => 'Open post in new tab',
    'gender_male' => 'his',
    'gender_female' => 'her',
    'buy_product' => 'Buy Product',
    'date_format' => 'd / M / Y','poll_total_votes' => 'Total votes: {total}',  
    'percentage' => '{percentage}%',       
    'option_text' => '{option}',                   
    'vote_the_option' => 'Vote for this option', 
    'gallery_view_image' => 'View Image',   
    'gallery_alt_image' => 'Image description', 
    'gallery_more_photos' => 'More Photos',
    'comments' => 'Comments',
    'shares' => 'Shares',

    'react' => 'React',
    'haha' => 'Haha',
    'wow' => 'Wow',
    'sad' => 'Sad',
    'angry' => 'Angry',
    'share' => 'Share',
    'share_on_facebook' => 'Share on Facebook',
    'share_on_x' => 'Share on X',
    'share_on_linkedin' => 'Share on LinkedIn',
    'post_on_timeline' => 'Post on Timeline',
    'copy_post_link' => 'Copy Post Link',
    'ad_image_alt' => 'Advertisement Image',
    'advertise_here' => 'Advertise Here',

    'no_posts' => 'No Posts',

    'people_you_may_know' => 'People you may know',
    'view_more' => 'View more',
    'articles_and_blogs' => 'Articles & Blogs',


    
    'share_your_thoughts' => 'Share your thoughts...',
    'add_colored_post' => 'Add a colored post',
    'where_are_you' => 'Where are you at?',
    'image_upload' => 'Image Upload',
    'video_upload' => 'Video Upload',
    'audio_upload' => 'Audio Upload',
    'update_profile' =>'Update Profile',
    'update_job_button' =>'Update Job ',


'login_account' => 'Log in to your account',
    'email_address' => 'Email Address',
    'remember_me' => 'Remember Me',
    'login' => 'Log in',
    'dont_have_account' => "Don't have an account?",
    'create_account' => 'Create an Account',
    'forgot_password' => 'Forgot Password',
    'sign_in_with_facebook' => 'Sign in with Facebook',
    'sign_in_with_google' => 'Sign in with Google',
    'quick_access' => 'Sign in with your social network for quick access',
    'admin_email' => '<EMAIL>',
    'admin_password' => '********',
    'user_email' => '<EMAIL>',
    'user_password' => '********',
    'role' => 'Role',
    'select_language'=>'Select Language',
    
    'create_new_account' => 'Create a new account',

    'date_of_birth' => 'Date of Birth',
    'register' => 'Register',
    'login_now' => 'Login Now',
    'already_have_account' => 'Already have an account?',
    'sign_in_quick_access' => 'Sign in with your social network for quick access',
    'no_registration' => 'We are sorry. We are not accepting any new registrations at the moment.',
    'password_complexity_info' => 'Include at least one uppercase, one lowercase, one special character, one number and 8 characters long.',
    'are_you_sure_unfriend'=>'Are you sure to unfriend this user?',
    'collected'=>'Collected',
    'donate'=>'Donate',
    
    'reset_password' => 'Reset Password',
    'reset' => 'Reset',
    'view_page' => 'View Page',
    'view_group' => 'View Group',
    'no_notifications' => 'No Notifications',
    'view'=>'View',
    'view_details' => 'View Details',
    'search_placeholder' => 'Search people and pages',

    'delete_account_permanently' => 'Delete account permanently',
    'enter_password' => 'Enter password',
    'delete_account_confirm_title' => 'Are you sure you want to delete?',
    'delete_account_confirm_text' => 'You are about to delete your account. If you delete your account, all your data will be lost',
    'delete_account_confirm_button' => 'Yes, delete it!',
    'settings_privacy' => 'Settings & Privacy',
    'upgrade_to_pro' => 'Upgrade to Pro',
    'sign_out' => 'Sign out',
        
    'notifications' => 'Notifications',
    'mark_all_read' => 'Mark all as read',
    'see_all' => 'See all',
    'option_1'=>'Option 1',
    'option_2'=>'Option 2',
    

    'cup_of_coffee' => 'Cup of Coffee',
    'great_job' => 'Great Job',
    'profile_info' => 'Profile Info',
    'born' => 'Born:',
    'phone_no' => 'Phone No:',
    'lives_in' => 'Lives in:',
    'validation' => [
        'name_required' => 'Name is required.',
        'location_required' => 'Location is required.',
        'start_date_required' => 'Start date is required.',
        'start_time_required' => 'Start time is required.',
        'end_date_required' => 'End date is required.',
        'end_time_required' => 'End time is required.',
        'start_date_time_before_current' => 'Start date/time must be after the current date/time.',
        'end_date_time_before_start' => 'End date/time must be after the start date/time.',
        'end_time_before_or_equal_start' => 'End time must be after the start time.',
        'error' => 'Error',
        'error_occurred' => 'An error occurred',
    ],
    'salary_range' => '{min_salary} - {max_salary} / {salary_date}',
    'view_applicants' => 'View Applicants',
    'delete_job' => 'Delete',
    'delete_job_confirm' => 'Are you sure to delete this Job?',
    'find'=>'Find',


    'no_data_found' => 'No data found',
    'select_blood_group' => 'Select Blood Group',


    'post_text' => 'Post Text',
    'update' => 'Update',
    'donate_amount' => 'Donate Amount',
    'share_post_on' => 'Share the post on',
    'my_timeline' => 'My Timeline',
    'page' => 'Page',
    'whats_going_on' => "What's going on?",
    'messaging' => 'Messaging',
    'loading_chat_list' => 'Loading Chat list',
    "only_video_files_allowed" => "Only video files are allowed. The selected file is not a video.",
    "invalid_data_received" => "Invalid data received in file-add event.",
    "only_audio_files_allowed" => "Only audio files are allowed. The selected file is not an audio.",


    "please_enter_some_text" => "Please enter some text",
    "comment_not_added" => "Comment could not be added",
    "error_adding_comment" => "An error occurred while adding the comment.",
    "failed_to_update_reaction" => "Failed to update reaction:",

    "comment_not_exist" => "Comment does not exist on this post.",
    "boost_visibility_heading" => "Boost Your Visibility: Advertise Under this Post",
    "advertisement_description" => "Get noticed by advertising under a popular post for only $1/month! Easy, affordable, and effective - your ad will be prominently displayed for 30 days. The fee is automatically deducted from your account. Boost your reach today!",
    "title_label" => "Title",
    "title_placeholder" => "Enter title",
    "link_label" => "Link",
    "link_placeholder" => "Enter link",
    "body_label" => "Body",
    "body_placeholder" => "Enter body",
    "image_label" => "Image",
    "image_recommended_size" => "Recommended size: 150x150",
    "submit_button" => "Submit",
    "form_submitted_successfully" => "Form submitted successfully!",
    "post_text_empty" => "Post text cannot be empty",
    "confirm_action" => "Are you sure to perform action?",
    "post_action" => "Post Action",

    "read_less" => "Read less...",
    "write_reply_placeholder" => "Write a reply...",
    "submit_reply" => "Submit Reply",
    "already_voted" => "Already voted Can not vote again and again",
    "warning" => "Warning",
    "donation_confirmation_text" => "Amount will be deducted from your wallet. Do you want to proceed with the donation?",
    "yes_proceed" => "Yes, proceed!",
    "donation_failed" => "Donation Failed",

    "add_comment_placeholder" => "Add a comment...",

    "no_more_posts" => "No More Posts to Show",

    "required_share_post_text" => "Please enter some text for your post.",
    "shared_text_placeholder" => "Enter your text here...",
    "just_now" => "Just Now",
    "delete_message" => "Delete this message",

    "type_a_message" => "Type a message...",
    "close_chat" => "Close chat",
    "online" => "Online",
    "delete_success" => "Success",
    "error_loading_conversations" => "Error Loading Conversations",
    "no_conversation" => "No Conversation",
    "find_friends" => "Find Friends",

    "urgent_need" => "Urgent Need",

    "comment_deleted_success" => "Comment deleted successfully!",
    'session_deleted'=>'Session deleted successfully',
    "delete_product_confirm" => "Are you sure you want to delete this product?",
    "no" => "No",

    "confirm_award" => "Are you sure to award ?",
    "available_balance" => "Your available balance is",
    "insufficient_balance" => "You do not have sufficient balance for this action.",
    "success_message_coc" => "Awarded the Cup of Coffee successfully!",
    "low_balance" => "Low Balance",
    "success_message_gj" => "Awarded the great job successfully!",
    "amount_deduction" => "and",
    "amount_deduction_message" => "amount would be deducted from your account.",
    'no_pokes_to_show' => 'No Pokes to show.',
    'needs' => 'Needs',
    'group_profile_info' => 'Group Profile Info',
    'members_count' => 'Members Count',
   
    "success_message" => "Awarded the Great Job successfully!",
    "yes_confirm" => "Yes!",
    "ok" => "OK",
    'change_language'=>'Change Language',
    "language_required" => "Please select a language.",
    "confirm_language_change_title" => "Confirm Language Change",
    "confirm_language_change_text" => "Are you sure you want to change the language?",
    'link_copy_clipboard'=>'Link copied to clipboard!',
    'followers' => 'Followers',
    'see_all_pages' => 'See All Pages',
    'see_all_followers' => 'See All Followers',
    'no_suggested_pages_found' => 'Suggested Page Not Found',
    'no_user_likes_page' => 'No User Like this Page',
    'like_page' => 'Are you sure you want to like this page?',
    'unlike_page' => 'Are you sure you want to unlike this page?',
    'remove_user' => 'Are you sure you want to remove this user from your page?',


    'edit_group' => 'Edit Group',
    'delete_group' => 'Delete Group',
    'leave_group' => 'Leave Group',
    'join_group' => 'Join Group',
   
    'are_you_sure_delete_group' => 'Are you sure you want to delete this group?',
    'are_you_sure_leave_group' => 'Are you sure you want to leave this group?',
    'are_you_sure_join_group' => 'Are you sure you want to join this group?',
    'see_all_members' => 'See all members',
    'see_all_groups' => 'See all Groups',
    'no_members_found' => 'No members found.',
    'suggested_group' => 'Suggested Group',
    'delete_confirm_button' => 'Yes, delete it!',
    'leave_confirm_button' => 'Yes, leave it!',
    'join_confirm_button' => 'Yes, join it!',
    'loading' => 'Loading...',
    'join_group_success' => 'Successfully joined the group!',
    'leave_group_success' => 'Successfully left the group!',
    'delete_group_success' => 'Group deleted successfully!',
    
    'group_members' => 'Group Members',
    'no_group_members_exist' => 'No Group Member Exists',
    'remove_admin' => 'Remove Admin',
    'make_admin' => 'Make Admin',
    'delete_user' => 'Delete',

    'page_followers' => 'Page Followers',
    'remove' => 'Remove',
    'no_user_liked' => 'No User Liked this page',
    "image_file_too_large"=> "The image file is too large. The maximum allowed size is 5MB.",
    'stripe_not_activated' => 'Stripe is not activated. Please contact platform admin',
    'paypal_not_activated' => 'Paypal is not activated. Please contact platform admin',
    'paystack_not_activated' => 'Paystack is not activated. Please contact platform admin',
    'delete_all'=>'Delete All',
    'all_games' => 'All Games',
    'no_games_found' => 'No games found',
    'release'=>'Release',

    'already_voted_cant_vote_again'=>'Already voted can not vote again and again',
    'start_chat'=>'to Start chat',
    'like_confirmation'=>'Are you sure to like this page?',
    'validation_min_salary_less_than_max' => 'Minimum salary must be less than maximum salary.',
    'deposit_via_flutterwave' => 'Deposit Via Flutterwave',
    'flutterwave' => 'Flutterwave',
    'flutterwave_not_activated' => 'Flutterwave not activated Contact Admin to activate.',
    'enter_email' => 'Enter email',
    'enter_name' => 'Enter name',
    'enter_amount' => 'Enter amount',
    'pay_with_flutterwave' => 'Pay with Flutterwave',
    'search_for_movies'=>'Search for Movies',
    'imdb'=>'IMDB',
    'movie_description'=>'Movie Description',
    'add_blood_request'=>'Add Blood request',
    'unlike_confirmation'=>'Are you sure to unlike this',
    'search_job_placeholder'=> 'Search Job',
    'content_unavailable'=>'This content is not available',
    'content_unavailable_reason'=>"This content isn't available right nowWhen this happens, it's usually because the owner only shared it with a small group of people, changed who can see it or it's been deleted.",
    "dark_theme" => "Dark Theme",
    'beginner' => 'Beginner', 
    'intermediate' => 'Intermediate', 
    'advanced' => 'Advanced',
    'is_paid' => 'Paid Course', 
    'amount_placeholder' => 'Enter the amount', 
    'create_course' => 'Create Course', 
    'course_title' => 'Course Title', 
    'course_title_required' => 'The course title is required.', 
    'course_address' => 'Course Address', 
    'course_address_optional' => 'Enter the course address (optional)', 
    'start_date_required' => 'The start date is required.', 
    'end_date_required' => 'The end date is required.', 
    'course_country' => 'Country', 
    'course_country_required' => 'The country is required.', 
    'course_cover_image' => 'Cover Image', 
    'course_description' => 'Description', 
    'course_description_optional' => 'Enter the course description (optional)', 
    'course_level' => 'Level', 
    'course_language' => 'Language', 
    'course_language_required' => 'The language is required.', 
    'course_category' => 'Category', 
    'create_course_button' => 'Create Course', 
    'course_created_successfully' => 'The course has been created successfully.', 
    'course_creation_error' => 'An error occurred while creating the course.', 
    'validation_course_title_required' => 'The course title is required.',
    'validation_course_title_minlength' => 'The course title must be at least 5 characters.',
    'validation_course_title_maxlength' => 'The course title cannot exceed 255 characters.',
    'validation_course_amount_required' => 'The amount is required.',
    'validation_course_start_date_required' => 'The start date is required.',
    'validation_course_end_date_required' => 'The end date is required.',
    'validation_course_country_required' => 'The country is required.',
    'validation_course_language_required' => 'The language is required.',
    'razorpay'=>'Razorpay',
    'courses' => 'Courses',
    'discover_courses' => 'Discover courses that help you grow your skills',
    'search_course_placeholder' => 'Search for a course...',
    'my_courses' => 'My Courses',
    'edit_course' => 'Edit Course',
    'delete_course' => 'Delete Course',
    'no_courses_found' => 'No courses found.',
    'list_all_courses' => 'List all courses',
    'delete_course_confirm' => 'Are you sure you want to delete this course?',
    'web_api' => 'Web API',
    'course_title_minlength' => 'Course title must be at least 5 characters long.',
    'course_title_maxlength' => 'Course title must not exceed 255 characters.',
    'course_amount_required' => 'Course amount is required.',
    'course_start_date_required' => 'Course start date is required.',
    'course_end_date_required' => 'Course end date is required.',
    'course_start_date_min' => 'Start date cannot be earlier than today.',
    'course_end_date_min' => 'End date cannot be earlier than today.',
    'course_categories'=>'Course Categories',
    'no_course_found' => 'Course not found',
    'list_all_course' => 'Voir tous les cours',
    'course' => 'Course',
    'find_a_course' => 'Find a Course',
    'find_course_opportunities' => 'Discover a world of learning opportunities',
    'you_needs' => 'Need',
    'course_price_not_zero' => 'Course price cannot be Zero',
    'level' => 'Level',
    'total_applicants_applied' => 'Total applicants applied',
    'course_apply_confirm' => 'Are you sure you want to apply?',
    'Poke' => 'Poke',
    'Fundings' => 'Fundings',

    'getting_started'=> 'Getting Started',
    'info_about_you'=> 'This information will let us know more about you.',
    'social_profiles'=> 'Social Profiles',
    'personal_details'=> 'Personal Details',
    'profile_picture'=> 'Profile Picture',
    'choose_image'=> 'Choose an image',
    'cover_picture'=> 'Cover Picture',
    'choose_cover_image'=> 'Choose a cover image',
    'skip'=> 'Skip',
    'social_network_presence'=> 'Your presence on the social network',
   
    'profile_complete'=> 'Profile Complete',
    'poll_title_required' => 'Poll title cannot be empty',
    'poll_option_required' => 'Poll options cannot be empty',

    'users' => 'Users',
    'groups' => 'Groups',
    'verified' => 'Verified',
    'unverified' => 'Unverified',
    'all' => 'All',
    'no_user_found' => 'No User Found',
    'no_page_found' => 'No Page Found',
    'no_group_found' => 'No Group Found',
   'register_success' =>"Registered successfully. Please log in your account to continue",
    'no_event_found' => 'No Event Found',
    'fill_all_fields' => 'Please fill in all fields.',
    'valid_donation_amount' => 'Please enter a valid donation amount.',
    'upload_donation_image' => 'Please upload a donation image.',
    'valid_image_format'=>'Please upload an image with a valid extension (jpg, jpeg, png, gif).',
    'terms' => 'Terms',
    'signup' => 'Sign Up',
    'data_deletion' => 'Data Deletion',
    'all_rights_reserved' => 'All rights reserved',
    'deposit' => 'Deposit',
    'poke' => 'Poke',
    'pokes' => 'Pokes',
    'poke_back' => 'Poke Back',
    'product_name_required' => 'Product name is required.',
    'location_required' => 'Location is required.',
    'price_required' => 'Price is required.',
    'price_number' => 'Price must be a valid number.',
    'units_required' => 'Units are required.',
    'units_number' => 'Units must be a valid number.',
    'images_required' => 'At least one image is required.',
    'images_accept' => 'Only JPEG and PNG image formats are allowed.',
    'custom_images_required_message' => 'You must select at least one image to proceed.',
    'delete_blood_request' => 'Delete Blood Request',
    'delete_blood_request_confirmation' => 'Do you want to delete this blood request?',
    'confirm' => 'Confirm',
    'blood_request_deleted' => 'Blood Request Deleted',
    'blood_request_deleted_message' => 'The blood request has been successfully deleted.',
    'cancelled' => 'Cancelled',
    'blood_request_cancelled_message' => 'Blood request deletion was cancelled.',
    'validation_course_start_date_min' => 'The start date must be today or later.',
    'validation_course_end_date_min' => 'The end date must be after the start date.',
    'photo_not_found' => 'Photo not found.',
    'video_not_found' => 'Video not found.',
    'keyword'=>'keyword',
    'block'=>'Block',
    
    'required_field' => 'This field is required.',
    'invalid_phone' => 'Please enter a valid phone number.',
    'phone_too_short' => 'Phone number is too short.',
    'phone_too_long' => 'Phone number is too long.',
    'file_required' => 'Please upload a file.',
    'submit_success_message' => 'Your application has been submitted successfully.',
'business_information'  => 'Business Information',
'zipcode'=> 'Zipcode',
'price_range'=> 'Price Range',
'facilities'=> 'Facilities',
'business_location'=> 'Business Location',
'pick_location' => 'Pick a Location',
'search_location_placeholder' => 'Search a location',
'avatar_alt' => 'User Avatar',
'page_avatar' => 'Page Avatar',
'page_pagination' => 'Page pagination',
'member' => 'member',
'please_select_at_least_one' => 'Please select at least one item.',
    'company' => 'Company',
    'area' => 'Area',
    'latitude' => 'Latitude',
    'longitude' => 'Longitude',
    'business_email' => 'Business Email',
    'fax' => 'Fax',
    'date_established' => 'Date Established',
    'business_category' => 'Business Category',
    'min_price' => 'Minimum Price',
    'max_price' => 'Maximum Price',
    'min_price_placeholder' => 'e.g. 100',
    'max_price_placeholder' => 'e.g. 1000',
    'page_type' => 'Page Type',
    'simple' => 'Simple',
    'tiktok' => 'TikTok',
    'city_required' => 'City is required.',
    'area_required' => 'Area is required.',
    'latitude_required' => 'Latitude is required.',
    'longitude_required' => 'Longitude is required.',
    'zipcode_required' => 'Zipcode is required.',
    'date_required' => 'Established date is required.',
    'min_price_required' => 'Minimum price is required.',
    'max_price_required' => 'Maximum price is required.',
    'min_less_than_max' => 'Minimum price must be less than maximum price.',
    'facilities_required' => 'Please select at least one facility.',
    'tags_required' => 'Please enter at least one tag.',
    'please_enter_two_charcters' => 'Please enter at least 2 characters.',
    'since' => 'Since',
    'company_minlength' => 'Company name must be at least 3 characters long.',
    'city_minlength'    => 'City must be at least 2 characters long.',
    'address_minlength' => 'Address must be at least 5 characters long.',
    'area_minlength'    => 'Area must be at least 2 characters long.',
    'phone_minlegth' => 'Phone number is too short.',
    'phone_maxlength' => 'Phone number is too long.',
    'business_category_required' => 'Business category is required.',
];
