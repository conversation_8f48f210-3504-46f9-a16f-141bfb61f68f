{"name": "flutterwavedev/flutterwave-v3", "description": "A simple SDK for integrating to Flutterwave Payment", "type": "library", "keywords": ["flutterwave", "payment", "transfers", "africa"], "homepage": "https://github.com/Flutterwave/Flutterwave-PHP-v3", "autoload": {"psr-4": {"Flutterwave\\": "src/"}}, "autoload-dev": {"Flutterwave\\Test\\": "tests/"}, "require": {"php": "^7.4 || ^8.0 || ^8.1", "monolog/monolog": "^2.0 || ^3.0", "vlucas/phpdotenv": "^2.5 || ^3.0 || ^5.0", "ext-json": "*", "ext-curl": "*", "ext-openssl": "*", "guzzlehttp/guzzle": "^7.5", "psr/http-client": "^1.0", "php-http/guzzle7-adapter": "^1.0", "composer/ca-bundle": "^1.3"}, "require-dev": {"phpunit/phpunit": ">=6.0", "mockery/mockery": ">=1.2", "symfony/var-dumper": "5.4.13", "phpstan/phpstan": "^1.9", "pestphp/pest": "^1.22", "nunomaduro/phpinsights": "^2.6", "eloquent/liberator": "^3.0", "squizlabs/php_codesniffer": "3.*", "dg/bypass-finals": "^1.4", "phpbench/phpbench": "^1.2"}, "license": "MIT", "authors": [{"name": "Flutterwave Developers", "email": "<EMAIL>"}], "config": {"allow-plugins": {"pestphp/pest-plugin": true, "dealerdirect/phpcodesniffer-composer-installer": true}}, "scripts": {"php-insight": ["./vendor/bin/phpinsights analyse src"], "php-insight-fix": ["vendor/bin/phpinsights fix src"], "phpcs": ["./vendor/bin/phpcs"], "phpcbf": ["./vendor/bin/phpcbf "], "test": ["./vendor/bin/pest"], "pest-filter": ["./vendor/bin/pest --filter"]}}