<?php

namespace App\Models;

use CodeIgniter\Model;

class PageTag extends Model
{
    protected $table            = 'page_tags';
    protected $primaryKey       = 'id';
    protected $useAutoIncrement = true;
    protected $returnType       = 'array';
    protected $useSoftDeletes   = true;
    protected $protectFields    = false;
    protected $allowedFields    = [];

    // Dates
    protected $useTimestamps = true;
    protected $dateFormat    = 'datetime';
    protected $createdField  = 'created_at';
    protected $updatedField  = 'updated_at';
    protected $deletedField  = 'deleted_at';

    // Validation
    protected $validationRules      = [];
    protected $validationMessages   = [];
    protected $skipValidation       = false;
    protected $cleanValidationRules = true;

    // Callbacks
    protected $allowCallbacks = true;
    protected $beforeInsert   = [];
    protected $afterInsert    = [];
    protected $beforeUpdate   = [];
    protected $afterUpdate    = [];
    protected $beforeFind     = [];
    protected $afterFind      = [];
    protected $beforeDelete   = [];
    protected $afterDelete    = [];
    public function getPageTag($pageId)
    {
        $tagModel = New TagModel();
        $ids = $this->select('tag_id')->where('page_id',$pageId)->findAll();
        if(empty($ids)){
            return [];
        }
          $idArray = array_column($ids, 'tag_id'); 
        $tags = $tagModel->select('name')->whereIn('id',$idArray)->findAll();
        return $tags;
    }
}
