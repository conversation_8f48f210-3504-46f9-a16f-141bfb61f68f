<?php

declare(strict_types=1);

namespace Flutterwave\Util;

class Currency
{
    public const NGN = 'NGN';
    public const USD = 'USD';
    public const UGX = 'UGX';
    public const KES = 'KES';
    public const ZAR = 'ZAR';
    public const ZMW = 'ZMW';
    public const EUR = 'EUR';
    public const GHS = 'GHS';
    public const TZS = 'TZS';
    public const RWF = 'RWF';
    public const XAF = 'XAF';
    public const XOF = 'XOF';
    public const EGP = 'EGP';
    public const GBP = 'GBP';
}
