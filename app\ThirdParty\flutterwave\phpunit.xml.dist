<?xml version="1.0" encoding="UTF-8"?>
<phpunit bootstrap="tests/bootstrap.php"
         backupGlobals="false"
         backupStaticAttributes="false"
         colors="true"
         verbose="true"
         convertErrorsToExceptions="true"
         convertNoticesToExceptions="true"
         convertWarningsToExceptions="true"
         processIsolation="false"
         stopOnFailure="false">
        
<testsuites>
    <testsuite name="Flutterwave Tests">
        <directory suffix=".php">tests</directory>
    </testsuite>
</testsuites>
<coverage processUncoveredFiles="true">
    <include>
        <directory suffix=".php">./src</directory>
    </include>
    <report>
        <clover outputFile="coverage.xml" />
    </report>
</coverage>


</phpunit>