<?php

namespace App\Models;

use CodeIgniter\Model;

class CourseApplicant extends Model
{
    protected $table            = 'course_applicants';
    protected $primaryKey       = 'id';
    protected $useAutoIncrement = true;
    protected $returnType       = 'array';
    protected $useSoftDeletes   = true;
    protected $protectFields    = false;
    protected $allowedFields    = [];

    // Dates
    protected $useTimestamps = false;
    protected $dateFormat    = 'datetime';
    protected $createdField  = 'created_at';
    protected $updatedField  = 'updated_at';
    protected $deletedField  = 'deleted_at';

    // Validation
    protected $validationRules      = [];
    protected $validationMessages   = [];
    protected $skipValidation       = false;
    protected $cleanValidationRules = true;

    protected $allowCallbacks = true;
    protected $beforeInsert   = [];
    protected $afterInsert    = [];
    protected $beforeUpdate   = [];
    protected $afterUpdate    = [];
    protected $beforeFind     = [];
    protected $afterFind      = [];
    protected $beforeDelete   = [];
    protected $afterDelete    = [];
    public function checkAlreadyApplied($user_id,$course_id)
    {
        $checkApplied   = $this->where(['user_id'=>$user_id,'course_id'=>$course_id])->first();
        return !empty($checkApplied)?1:0;
    }

    public function getApplicants($id)
    {
        $checkApplied   = $this->where(['course_id'=>$id])->findAll();
        return $checkApplied;
    }

    public function getApplicantDetail($course_id)
    {
        $db = $this->db;
        $dbPrefix = $db->DBPrefix;
    
        return $this->select([$dbPrefix.'users.username', $dbPrefix.'users.email', $dbPrefix.'users.avatar', $dbPrefix.'users.gender',$dbPrefix.'users.phone',$dbPrefix.'users.address as location', $dbPrefix.'course_applicants.*'])
            ->join($dbPrefix.'users', $dbPrefix.'users.id = '.$dbPrefix.'course_applicants.user_id')
            ->where($dbPrefix.'course_applicants.course_id', $course_id)
            ->where($dbPrefix.'users.deleted_at', null)
            ->findAll();
    }
    public function  getAllCourseApplicants($id)
    {
        $userIds = $this->select('user_id')->where('course_id',$id)->findAll();
        if(count($userIds)>0)
        {
            $userIds = array_column($userIds,'user_id');
           foreach($userIds as $userId)
           {
                $userModel  =  New UserModel();
                $users[] = $userModel->getUserShortInfo($userId);
            
           }
           return $users;
        }
        return null;
    }
}
