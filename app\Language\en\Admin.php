<?php
return [
    'dashboard' => 'Dashboard',
    'setting' => 'Settings',
    'website_information' => 'Website Information',
    'enabledisable_feature' => 'Enable/Disable Features',
    'general_setting' => 'General Settings',
    'mail_configuration' => 'Mail Configuration',
    'gateway_integration' => 'Gateway Integration',
    'storage_configuration' => 'Storage Configuration',
    'social_login'=>'Social Login',
    'manage_users'=>'Manage Users',
    'online_users'=>'Online Users',
    'verified_users'=>'Verified Users',
    'unverified_users'=>'Unverified Users',
    'packages'=>'Packages',
    'movies'=>'Movies',
    'manage_ads'=>'Manage Ads',
    'manage_user_ads'=>'Manage User Ads',
    'pages'=>'Pages',
    'groups'=>'Groups',
    'games'=>'Games ',
    'tools'=>'Tools',
    'auto_like_page'=>'Auto Like Page',
    'auto_friend'=>'Auto Friend',
    'auto_delete'=>'Auto Delete',
    'auto_join'=>'Auto Join Group',
    'linkedin_login'=>'Linkedin Login',
    'withdrawals' =>'Withdrawls',
    'deposits' =>'Deposits',
    'manage_reports'=>'Manage Reports',
    'post_reports' =>"Post's Reports",
    'user_report' =>"User's Reports",
    'manage_posts'=> "Manage Posts",
    'posts'=> "Posts",
    'custom_pages' =>'Custom Pages',
    'blogs'=>'Blogs',
    'gifts'=>'Gifts',
    'jobs'=>'Jobs',
    'filters'=>'Filters',
    'products'=>'Products',
    'space'=>'Space',
    'spaces'=>'Spaces',
    'events'=>'Events',
    'event'=>'Event',
    'blood'=>'Blood',
    'blood_requests'=>'Blood Requests',
    'blood_request'=>'Blood Request',
    'blood_donors'=>'Blood donors',
    'post_advertisement'=>'Post Advertisement',
    'system_status'=>'System Status',
    'logout' =>'Logout',
    'welcome'=>'Welcome',
    'welcome_back'=>'Welcome back',
    'latest_wthdraw_requeest' =>'Latest Withdrawl Requests',
    'latest_deposit_requeest' =>'Latest Deposit Requests',
    'name'=>'Name',
    'total_users'=>'Total Users',
    'pending_withdraw'=>'Pending Withdraws',
    'total_jobs'=>'Total Jobs',
    'total_posts'=>'Total Posts',
    'total_groups'=>'Total Groups',
    'total_pages'=>'Total Pages',
    'status' =>"Status",
    'request_amount' =>"Request Amount",
    'request_date' =>"Request Date",
    'no_withdraw_found' =>'No Withdrawl request Found',
    'more_info'=>'More Info',
    'in_progress'=>'In Progress',
    'completed'=>'Completed',
    'canceled' =>'Canceled',
    'lottie_animation' => 'Lottie Animation',
    'confirm_delete_event' => 'Are you sure you want to delete this event?',
    'delete' => 'Delete',
    'website_name'=> 'Website Name',
    'defualt_language'=> 'Default Language',
    'defualt_language_description'=> 'Set Default Language ',
    'website_title'=> 'Website Title',
    'general'=>'General',
    'website_name_description'=>"Website name, it will on website's footer and E-mails.",
    'website_title_description'=>"Your website title will appear on Google and on your browser tab.",
    'website_keyword'=>'Website Keywords',
    'website_keyword_description'=>"Your website's keyword, used mostly for SEO and search engines.",
    'website_description'=>'Website Description',
    'website_desc_details' =>"Your website's description, used mostly for SEO and search engines, Max of 150 characters is recommended",
    'website_analytics' =>"Paste your full Google Analytics Code here to track traffic.",
    'max_file_upload_size'=>'Maximum File Upload Size(MB)',
    'max_file_upload_description'=>'You can set the limit for File Upload Size(MB)',
    'og_title'=> "OG Title",
    'og_title_desc'=> "Title of a webpage when shared on social media platforms like Facebook.",
    'og_description'=> "OG Description",
    'og_description_details'=> "General OG Description of a webpage's content for optimized display on social media platforms.",
    'login_page_text'=>'Login Page Text',
    'logo_favicon'=>'Logo And Favicon',
    'logo_favicon_desc'=>'Logo And Favicon',
    'website_logo'=>'Website Logo',
    'favicon'=>'Favicon',
    'upload'=>'Upload',
    'post_adv_description'=>' Show Post Advertisement and Tax and Shares',
    'ads_price'=>'Ads Price ($)',
    'ads_price_description'=>'You can add Ads Price (price for run ad )',
    'post_owner_share'=>'Post Owner Share (%)',
    'post_owner_share_desc'=>'Set Post Owner Share in %age',
    'admin_share'=>'Post Admin Share (%)',
    'admin_share_desc'=>'Set Admin Share in %age',
    'google_advertisement'=>'Google Advertisment',
    'google_advertisement_key'=>'Google Advertisment Key',
    'google_advertisement_desc'=>'Add Google Advertisemnt Key to run Google Ads on your website',
    'greeting_system' => 'Greeting System',
    'greeting_system_desc' => 'Enable/Disable Morning, Afternoon & Evening messages on Homepage.',
    'stories_system' => 'Stories System',
    'stories_system_desc' => 'Enable/Disable stories Section on user Side in Web & App.',
    'games_system' => 'Games System',
    'games_system_desc' => 'Enable/Disable Gaming Section on user Side in Web & App.',
    'pages_system' => 'Pages System',
    'pages_system_desc' => 'Enable/Disable Page Section on user Side in Web & App.',
    'groups_system' => 'Groups System',
    'groups_system_desc' => 'Enable/Disable Group Section on user Side in Web & App.',
    'blogs_system' => 'Blogs System',
    'blogs_system_desc' => 'Enable/Disable Blogs Section on user Side in Web & App.',
    'events_system' => 'Events System',
    'events_system_desc' => 'Enable/Disable Events Section on user Side in Web & App.',
    'movies_system' => 'Movies System',
    'movies_system_desc' => 'Enable/Disable Movies Section on user Side in Web & App.',
    'nearby_friends_system' => 'Nearby Friends System',
    'nearby_friends_system_desc' => 'Allow users to search nearby users.',
    'jobs_system' => 'Jobs System',
    'jobs_system_desc' => 'Enable/Disable Jobs Section on user Side in Web & App.',
    'friend_system' => 'Friend System',
    'friend_system_desc' => 'Enable/Disable Friend Section on user Side in Web & App.',
    'market_place' => 'Market Place',
    'market_place_desc' => 'Enable/Disable Market Place Section on user Side in Web & App.',
    'blood_donation' => 'Blood Donation',
    'blood_donation_desc' => 'Enable/Disable Blood Donation Section on user Side in Web & App.',
    'wallet' => 'Wallet',
    'wallet_desc' => 'Enable/Disable whole wallet Section on user Side in Web & App.',
    'upgrade_to_pro_system' => 'Upgrade to Pro System',
    'package_system' => 'Package System',
    'package_system_desc' => 'Enable/Disable Package System.',
    'upgrade_to_pro_system_desc' => 'Allow/Disallow update to pro system from user side.',
    'email_verification_system' => 'Email Verification System',
    'allow_user_registration' => 'Allow User Registration',
    'allow_user_registration_desc' => 'Allow users to create accounts on a platform or website.',
    'verify_new_register_account' => 'Verify New Register Account',
    'verify_new_register_account_desc' => 'Allow/Disallow the new register user to verify their account.',
    'developer_mode' => 'Developer Mode',
    'developer_mode_desc' => 'By enabling Developer Mode, error reporting will be enabled. It is not recommended to enable this mode without the help of a developer, as it may cause issues on your website.',
    'maintenance_mode' => 'Maintenance Mode',
    'maintenance_mode_desc' => 'Turn the whole site under Maintenance. You can get the site back by visiting /admincp or /admin-cp.',
    'welcome_page_users' => 'Welcome Page Users',
    'welcome_page_users_desc' => 'Turn the whole site under Maintenance. You can get the site back by visiting /admincp or /admin-cp.',
    'reserved_usernames_system' => 'Reserved Usernames System',
    'reserved_usernames_system_desc' => 'By enabling the Reserved Usernames System, maintain a list of reserved usernames and validate new username submissions against this list.',
    'censored_words' => 'Censored Words',
    'censored_words_placeholder' => 'Enter censored words here...',
    'login_registration' => 'Login & Registration',
    'user_registration' => 'User Registration',
    'user_registration_desc' => 'By enabling User Registration, error reporting will be enabled. It is not recommended to enable this mode without the help of a developer, as it may cause issues on your website.',
    'password_complexity_system' => 'Password Complexity System',
    'password_complexity_system_desc' => 'To enable or disable a password complexity system in an application, toggle a boolean variable controlling the system.',
    'recaptcha' => 'reCaptcha',
    'recaptcha_desc' => 'Enhances security by verifying users are human before allowing certain actions.',
    'recaptcha_sitekey' => 'reCaptcha Site key',
    'recaptcha_secret_key' => 'reCaptcha Secret key',
    'user_configuration' => 'User Configuration',
    'user_account_deletion' => 'User Account Deletion',
    'user_account_deletion_desc' => 'Permanently removing a user\'s account and associated data from a platform or website.',
    'notifications_settings' => 'Notifications Settings',
    'email_notifications' => 'E-mail Notifications',
    'email_notifications_desc' => 'By enabling E-mail Notifications, automated messages sent via email to inform users about relevant updates, events, or activities on a platform or website.',
    'profile_visit_notifications' => 'Profile Visit Notifications',
    'profile_visit_notifications_desc' => 'By enabling Profile Visit Notifications, alerts or notifications sent to a user when someone else visits their profile.',
    'notification_on_new_post' => 'Notification On New Post',
    'notification_on_new_post_desc' => 'By enabling Notification On New Post, alerts or messages sent to users when a new post is published on a website or platform, informing them of the latest content.',
    'exchangerate_api_key' => 'Exchangerate API Key',
    'coming_soon' => 'Coming Soon',
    'collapse' => 'Collapse',
    'protocol' => 'Protocol',
    'smtp' => 'SMTP',
    'sendmail' => 'SendMail',
    'smtp_host' => 'SMTP Host',
    'smtp_port' => 'SMTP Port',
    'smtp_username' => 'SMTP Username',
    'smtp_password' => 'SMTP Password',
    'smtp_encryption' => 'SMTP Encryption',
    'tls' => 'TLS',
    'ssl' => 'SSL',
    'from_address' => 'From Address',
    'from_name' => 'From Name',
    'save_changes' => 'Save Changes',
    'paypal' => 'Paypal',
    'paypal_client_id' => 'PayPal Client ID',
    'paypal_client_secret' => 'Paypal Client Secret',
    'stripe' => 'Stripe',
    'stripe_public_key' => 'Stripe Public Key',
    'stripe_secret_key' => 'Stripe Secret Key',
    'paystack' => 'Paystack',
    'paystack_public_key' => 'Paystack Public Key',
    'paystack_secret_key' => 'Paystack Secret Key',
    'flutterwave' => 'Flutterwave',
    'flutterwave_public_key' => 'Flutterwave Public Key',
    'flutterwave_secret_key' => 'Flutterwave Secret Key',
    'local_storage' => 'Local Storage',
    'aws' => 'AWS',
    'aws_bucket_name' => 'Amazon Bucket Name',
    'aws_s3_key' => 'Amazon S3 Key',
    'aws_s3_secret_key' => 'Amazon S3 Secret Key',
    'aws_bucket_region' => 'Amazon Bucket Region',
    'update_aws_storage' => 'Update AWS Storage',
    'wasabi_storage' => 'Wasabi Storage',
    'wasabi_bucket_name' => 'Wasabi Bucket Name',
    'wasabi_access_key' => 'Wasabi Access Key',
    'wasabi_secret_key' => 'Wasabi Secret Key',
    'wasabi_bucket_region' => 'Wasabi Bucket Region',
    'update_wasabi_storage' => 'Update Wasabi Storage',
    'ftp_storage' => 'FTP Storage',
    'ftp_hostname' => 'FTP Hostname',
    'ftp_username' => 'FTP Username',
    'ftp_password' => 'FTP Password',
    'ftp_port' => 'FTP Port',
    'ftp_endpoint' => 'FTP Endpoint',
    'update_ftp_settings' => 'Update FTP Settings',
    'digital_ocean_storage' => 'Digital Ocean Storage',
    'space_key' => 'Space Key',
    'space_secret_key' => 'Space Secret Key',
    'space_name' => 'Space Name',
    'space_region' => 'Space Region',
    'update_digital_ocean_storage' => 'Update Digital Ocean Storage',
    'edit_user'=>'Edit User',
    'google_login' => 'Google Login',
    'google_client_id' => 'Google Client ID',
    'google_client_secret' => 'Google Client Secret',
    'google_client_id_placeholder' => 'Google Client ID',
    'google_client_secret_placeholder' => 'Google Client Secret',
    'all_events'=>'All Events',
    'facebook_login' => 'Facebook Login',
    'facebook_client_id' => 'Facebook Client ID',
    'facebook_client_secret' => 'Facebook Client Secret',
    'facebook_client_id_placeholder' => 'Facebook Client ID',
    'facebook_client_secret_placeholder' => 'Facebook Client Secret',
    'edit_custom_page'=>'Edit Custom Page',
    'package_information'=>'Package Information',
    'user_management' => 'User Management',
    'search_label' => 'Search by Username, Email, Name',
    'search_placeholder' => 'Search users...',
    'user_type' => 'User Type',
    'all_users' => 'All Users',
    'active' => 'Active',
    'inactive' => 'Inactive',
    'search' => 'Search',
    'reset' => 'Reset',
    'id' => 'ID',
    'username' => 'User Name',
    'email' => 'Email',
    'role' => 'Role',
    'action' => 'Action',
    'user' => 'User',
    'admin' => 'Admin',
    'assign_role' => 'Assign Role',
    'edit' => 'Edit',
    'change_password' => 'Change Password',
    'are_you_sure' => 'Are you sure?',
    'create_new_package' => 'Create New Package',
    'package_name' => 'Package Name',
    'package_description' => 'Package Description',
    'like_amount' => 'Like Amount',
    'share_amount' => 'Share Amount',
    'comment_amount' => 'Comment Amount',
    'manage_or_customize_movies' => 'Manage or Customize Movies',
    'confirm_delete' => 'Are you sure to delete this record?',
    'package_name_placeholder' => 'Enter package name',
    'package_color' => 'Package Color',
    'like_amount_placeholder' => 'Enter like amount',
    'po_like_amount' => 'Post Owner Like Share',
    'po_like_amount_placeholder' => 'Enter post owner like share amount',
    'comment_amount_placeholder' => 'Enter comment amount',
    'po_comment_amount' => 'Post Owner Comment Share',
    'po_comment_amount_placeholder' => 'Enter post owner comment share amount',
    'share_amount_placeholder' => 'Enter share amount',
    'po_share_amount' => 'Post Owner Share Amount',
    'po_share_amount_placeholder' => 'Enter post owner share amount',
    'package_price' => 'Package Price',
    'package_price_placeholder' => 'Enter package price',
    'point_spendable' => 'Point Spendable',
    'point_spendable_placeholder' => 'Enter point spendable',
    'duration' => 'Duration',
    'day' => 'Day',
    'week' => 'Week',
    'month' => 'Month',
    'year' => 'Year',
    'video_size' => 'Video Size',
    'longer_post' => 'Longer Post',
    'featured_members' => 'Featured Members',
    'edit_post' => 'Edit Post',
    'post_promotion' => 'Post Promotion',
    'page_promotion' => 'Page Promotion',
    'verified_badge' => 'Verified Badge',
    'submit' => 'Submit',
    'cancel' => 'Cancel',
    'modal_title' => 'Modal Title',
    'close' => 'Close',
    'auto_user_friends_updated' => 'Auto user as friends are updated',
    'auto_join_group_updated' => 'Auto join group are updated',
    'visit_site' => 'Visit Site',
    'new_window' => 'New Window',
    'current_window' => 'Current Window',
    'movies_list' => 'Movies List',
    'create_new_movie' => 'Create New Movie',
    'type' => 'Type',
    'stars' => 'Stars',
    'producer' => 'Producer',
    'actions' => 'Actions',
    'manage_or_customize_package'=>'Manage Or Customize Package',
    'page_name' => 'Page Name',
    'page_title' => 'Page Title',
    'page_description' => 'Page Description',
    'page_username' => 'Page Username',
    'website' => 'Website',
    'company' => 'Company',
    'page_cover' => 'Page Cover',
    'page_avatar' => 'Page Avatar',
    'page_category' => 'Page Categories',
    'select_category' => 'Select page category',
    'title_placeholder' => 'Title',
    'username_placeholder' => 'page',
    'website_placeholder' => 'Website Link',
    'company_placeholder' => 'Company',
    'group_title' => 'Group Title',
    'group_category' => 'Group Category',
    'group_description' => 'Group Description',
    'create_new_group' => 'Create New Group',
    'users' => 'Users',
    'group_name' => 'Group Name',
    'group_cover' => 'Group Cover',
    'group_avatar' => 'Group Avatar',
    'privacy' => 'Privacy',
    'public' => 'Public',
    'private' => 'Private',
    'name_placeholder' => 'Title',
    'description' => 'Description',
    'link' => 'Link',
    'create_new_game' => 'Create New Game',
    'edit_game' => 'Edit Game',
    'confirm_delete_game' => 'Are you sure to delete this game?',
    'auto_page_like' => 'Auto Page Like',
    'backup_recommendation' => 'Its recommended to get a backup before applying any actions.',
    'page_names' => 'Page Name(s)',
    'usernames' => 'Username(s)',
    'auto_delete_website_data' => 'Auto Delete Website Data',
    'choose_one' => 'Choose One',
    'delete_inactive_users' => 'Delete all inactive users',
    'delete_users_not_logged_in_1_week' => 'Delete users that are not logged in more than 1 week',
    'delete_users_not_logged_in_1_month' => 'Delete users that are not logged in more than 1 month',
    'delete_users_not_logged_in_1_year' => 'Delete users that are not logged in more than 1 year',
    'delete_posts_older_than_1_week' => 'Delete posts that are older than 1 week',
    'delete_posts_older_than_1_month' => 'Delete posts that are older than 1 month',
    'delete_posts_older_than_1_year' => 'Delete posts that are older than 1 year',
    'process_warning' => 'This process might take some time, you can check for your site changes after a few minutes.',
    'withdraw_via' => 'Withdraw Via',
    'amount' => 'Amount',
    'pending' => 'Pending',
    'approved' => 'Approved',
    'rejected' => 'Rejected',
    'approve' => 'Approve',
    'reject' => 'Reject',
    'details' => 'Details',
    'paypal_email' => 'Paypal Email',
    'country' => 'Country',
    'full_name' => 'Full Name',
    'iban' => 'IBAN',
    'swift_code' => 'Swift Code',
    'deposit_via' => 'Deposit Via',
    'post' => 'Post',
    'post_views' => 'Post Views',
    'from_user' => 'From User',
    'reported_user' => 'Reported User',
    'reason' => 'Reason',
    'post_text' => 'Post Text',
    'post_type' => 'Post Type',
    'shared_post' => 'Shared Post',
    'yes' => 'Yes',
    'no' => 'No',
    'create_new_post' => 'Create New Post',
    'meta_title' => 'Meta Title',
    'create_new_page' => 'Create New Page',
    'no_custom_page_found' => 'No Custom Page Found',
    'page_title_placeholder' => 'Enter Page Title',
    'meta_title_placeholder' => 'Enter Meta Title',
    'meta_description' => 'Meta Description',
    'meta_description_placeholder' => 'Enter Meta Description',
    'page_content' => 'Page Content',
    'page_content_placeholder' => 'Enter Page Content',
    'create' => 'Create',
    'update' => 'Update',
    'blog_list' => 'Blog List',
    'title' => 'Title',
    'category' => 'Category',
    'create_new_blog' => 'Create New Blog',
    'create_blog' => 'Create Blog',
    'enter_title' => 'Enter title',
    'image' => 'Image',
    'content' => 'Content',
    'tags' => 'Blog Tag (Comma-separated)',
    'enter_tags' => 'e.g., tag1, tag2, tag3',
    'edit_blog' => 'Edit Blog',
    'blog_tags' => 'Blog Tag (Comma-separated)',
    'add_new_gift' => 'Add New Gift',
    'gift_name' => 'Gift Name',
    'gift_price' => 'Gift Price',
    'gift_image' => 'Gift Image',
    'gift_name_placeholder' => 'Enter gift name',
    'gift_image_placeholder' => 'Choose an image for the gift',
    'gift_price_placeholder' => 'Enter gift price',
    'job_title' => 'Job Title',
    'job_description' => 'Job Description',
    'job_location' => 'Job Location',
    'job_category' => 'Job Category',
    'select_job_category' => 'Select Job Category',
    'minimum_salary' => 'Minimum Salary',
    'maximum_salary' => 'Maximum Salary',
    'salary_date' => 'Salary Date',
    'currency' => 'Currency',
    'select_currency' => 'Select Currency',
    'is_urgent_hiring' => 'Is Urgent Hiring',
    'experience_years' => 'Experience Years',
    'company_name' => 'Company Name',
    'job_type' => 'Job Type',
    'expiry_date' => 'Expiry Date',
    'back' => 'Back',
    'gender' => 'Gender',
    'profile' => 'Profile',
    'phone' => 'Phone No',
    'cv' => 'CV',
    'address' => 'Address',
    'download_cv' => 'Download CV',
    'no_applicant' => 'No user has applied for this job',
    'filter_list_title' => 'Filters List',
    'create_new_filter' => 'Create New Filter',
    'no_data_found' => 'No Data Found',
    'create_filter' => 'Create Filter',
    'enter_name' => 'Enter name',
    'enter_link' => 'Enter link',
    'name_required' => 'Please enter the name',
    'name_minlength' => 'Name must be at least 3 characters',
    'name_maxlength' => 'Name cannot exceed 255 characters',
    'link_required' => 'Please enter the link',
    'edit_filter' => 'Edit Filter',
    'product_name' => 'Product Name',
    'price' => 'Price',
    'view' => 'View',
    'view_details' => 'View Details',
    'confirm_delete_product' => 'Are you sure to delete this product?',
    'no_products_found' => 'No products found',
    'product_images' => 'Product Images',
    'product_image_alt' => 'Product Image',
    'user_details' => 'User Details',
    'user_profile' => 'User Profile',
    'user_avatar_alt' => 'User Avatar',
    'cover' => 'Cover',
    'user_cover_alt' => 'User Cover',
    'product_details' => 'Product Details',
    'condition' => 'Condition',
    'new' => 'New',
    'used' => 'Used',
    'location' => 'Location',
    'units' => 'Units',
    'space_title' => 'Space Title',
    'paid_or_free' => 'Paid or Free',
    'ongoing_space' => 'Ongoing Space',
    'paid' => 'Paid',
    'free' => 'Free',
    'space_detail' => 'Space Detail',
    'space_members' => 'Space Members',
    'profile_image' => 'Profile Image',
    'cover_image' => 'Cover Image',
    'space_details' => 'Space Details',
    'paid_free' => 'Paid/Free',
    'continue' => 'Continue',
    'friend' => 'Friend',
    'sr_no' => 'Sr #',
    'user_image' => 'User Image',
    'user_role' => 'User Role',
    'can_speak' => 'Can Speak',
    'space_host' => 'Space Host',
    'space_cohost' => 'Space Co-host',
    'listener' => 'Listener',
    'no_user_in_space' => 'No User is in this Space',
    'event_name' => 'Event Name',
    'event_location' => 'Event Location',
    'create_new_event' => 'Create New Event',
    'going_user' => 'Going User',
    'interested_user' => 'Interested User',
    'user_name' => 'User Name',
    'no_user_going' => 'No user going to this event',
    'no_user_interested' => 'No user showing interest in this event',
    'event_description' => 'Event Description',
    'url' => 'URL',
    'start_date' => 'Start Date',
    'start_time' => 'Start Time',
    'end_date' => 'End Date',
    'end_time' => 'End Time',
    'blood_group' => 'Blood Group',
    'urgently_needed' => 'Urgently Needed',
    'donation_available' => 'Donation Available',
    'ad_title' => 'Ad Title',
    'ad_image' => 'Ad Image',
    'no_advertisement_found' => 'No Advertisement found',
    'systemStatusImageAlt' => 'System Status Image',
    'systemPerfectCondition' => 'Your system is in perfect condition',
    'copyright' => 'Copyright',
    'allRightsReserved' => 'All rights reserved.',
    'version' => 'Version',
    'errorValidating' => 'Error Validating',
    'demoRestriction' => "You can't do this action in demo.",
    'keyAndValueRequired' => 'Key and value are required',
    'settingUpdatedSuccessfully' => 'Setting updated successfully',
    'withdraw_requests' => 'Withdraw Requests',
    'general_settings' => 'General Settings',
    'withdraw_request_approved' => 'Withdraw Request is approved',
    'withdraw_request_rejected' => 'Withdraw Request is rejected',
    'withdraw_request_details' => 'Withdraw Request Details',
    'all_spaces' => 'All Spaces',
    'manage_spaces' => 'Manage spaces',
    'space_deleted' => 'Space is deleted',
    'payment_gateway_integration' => 'Payment Gateway Integration',
    'social_login_integration' => 'Social Login Integration',
    'demo_mode_error' => 'Can not update in demo mode',
    'aws_storage_updated' => 'AWS Storage is updated',
    'wasabi_storage_updated' => 'Wasabi Storage is updated',
    'ftp_storage_updated' => 'FTP Storage is updated',
    'space_storage_updated' => 'Digital Ocean Storage is updated',
    'all_reports' => 'All Reports',
    'post_report' => 'Post Report',
    'report_action_success' => 'Report is :actioned',
    'all_reported_users' => 'All Reported Users',
    'user_deletion_error' => 'User can not be deleted due to: :error',
    'all_products' => 'All Products',
    'manage_products' => 'Manage Products',
    'product_detail' => 'Product Detail',
    'product_deleted_successfully' => 'Product deleted successfully',
    'all_posts' => 'All posts',
    'post_deleted_successfully' => 'Post deleted successfully',
    'manage_customize_pages' => 'Manage Or Customize Pages',
    'edit_page' => 'Edit Page',
    'page_updated_successfully' => 'Page is updated',
    'page_deleted_successfully' => 'Page is deleted',
    'edit_movie' =>'Edit Movie',
    'add_movie' =>'Add Movie',
    'all_jobs' =>'All Jobs ',
    'edit_job' =>'Edit Job',
    'job_applicants'=>'Job Applicants',
    'package_message' => [
        'name_required' => 'The Name field is required.',
        'description_required' => 'The Description field is required.',
        'like_amount_required' => 'The Like amount is required.',
        'share_amount_required' => 'The Share amount is required.',
        'comment_amount_required' => 'The Comment amount is required.',
        'po_like_amount_required' => 'The Post like amount is required.',
        'po_share_amount_required' => 'The Post share amount is required.',
        'po_comment_amount_required' => 'The Post comment amount is required.',
        'package_price_required' => 'The Package price field is required.',
      
    ],
    'movie_message' => [
        'movie_name_required' => 'The movie name is required.',
        'genre_required' => 'The genre is required.',
        'stars_required' => 'The stars field is required.',
        'producer_required' => 'The producer is required.',
        'duration_required' => 'The duration is required.',
        'description_required' => 'The description is required.',
        'cover_pic_uploaded' => 'You must upload a cover picture.',
        'cover_pic_mime_in' => 'The cover picture must be a valid image (jpg, png, jpeg, gif).',
        'video_uploaded' => 'You must upload a video.',
        'video_mime_in' => 'The video must be a valid MP4 file.',
    ],
    'gateway_message' => [
        'name_required' => 'The Name field is required.',
        'currency_required' => 'The Currency field is required.',
        'logo_uploaded' => 'You must upload a valid logo.',
        'logo_mime_in' => 'The logo must be a valid image (jpg, png, jpeg, gif).',
    ],
    'game_message' => [
        'name_required' => 'The Name field is required.',
        'description_required' => 'The Description field is required.',
        'url_required' => 'The URL field is required.',
        'image_uploaded' => 'You must upload a valid image.',
        'image_mime_in' => 'The image must be a valid image (jpg, png, jpeg, gif).',
    ],
    'filter_message' => [
        'name_required' => 'The Name field is required.',
        'link_required' => 'The Link field is required.',
        'image_uploaded' => 'You must upload a valid image.',
        'image_mime_in' => 'The image must be a valid image (jpg, png, jpeg, gif).',
    ],
    'all_groups'=>'All Groups',
    'edit_group'=>'Edit Group',
    'group_members'=>'Group Members',
    'all_gifts'=>'All Gifts',
    'create_gift'=>'Create Gift',
    'edit_gift'=>'Edit Gift',
    'manage_gifts'=>'Manage or customize Gifts',
    'update_gift_success'=>'Gift updated successfully',
    'delete_gift_success'=>'Gift deleted successfully',
    'update_gift_error'=>'Failed to Update the Gift',
    'delete_gift_error'=>'Failed to delete the Gift',
    'user_dashboard'=> 'Dashboard',
    'all_gateways'=>'All Payment Gateways',
    'create_gateway'=>'Create Gateway',
    'all_games'=>'All Games',
    'manage_games' => 'Manage or customize games',
    'create_game_success' => 'Game created successfully',
    'update_game_success' => 'Game updated successfully',
    'delete_game_success' => 'Game deleted successfully',
    'all_filters' => 'All Filters',
    'add_filter' => 'Add Filter',
    'create_filter_success' => 'Filter created successfully',
    'update_filter_success' => 'Filter updated successfully',
    'delete_filter_success' => 'Filter deleted successfully',
    'update_event_success' => 'Event updated successfully',
    'delete_event_success' => 'Event deleted successfully',
    'edit_event' => 'Edit event',
    'event_message' => [
        'name_required' => 'The Name field is required.',
        'location_required' => 'The Location field is required.',
        'start_date_required' => 'The Start Date field is required.',
        'start_time_required' => 'The Start Time field is required.',
        'end_date_required' => 'The End Date field is required.',
        'end_time_required' => 'The End Time field is required.',
        'cover_uploaded' => 'You must upload a valid cover image.',
        'cover_mime_in' => 'The cover image must be a valid image (jpg, jpeg, png).',
        // Add other messages as needed
    ],
    'event_details' => 'Event Details',
    'deposit_requests' => 'Deposit Requests',
    'deposits_list' => 'Deposits List',
    'deposit_request_details' => 'Deposit Request Details',
    'manage_custom_pages' => 'Manage or customize Custom Pages',
    'create_new_custom_page' => 'Create New Custom Page',
    'create_custom_page' => 'Create Custom Page',
    'custom_page_message' => [
        'page_title_required' => 'The Page Title field is required.',
        'create_error' => 'Failed to create Custom Page.',
        'create_new_package' => 'Create New Package',
        'package_information' => 'Package Information',
    ],
    'create_custompage_success' => 'Custom Page is created successfully.',
    'update_group_success' => 'Group is updated successfully.',
    'delete_group_success' => 'Group deleted successfully.',
    'delete_custompage_success' => 'Custom Page is created successfully.',
    'update_custompage_success' => 'Custom Page is updated successfully.',
    'create_custompage_error' => 'Faild to create custopage.',
    'delete_custompage_error' => 'Faild to create custopage.',
    'update_custompage_error' => 'Faild to update custopage.',
    'all_blood_requests' => 'All Blood Requests',
    'manage_blood_requests' => 'Manage blood requests',
    'all_blood_donors' => 'All Blood Donors',
    'manage_blood_donors' => 'Manage Blood Donors',
    'all_blogs' => 'All Blogs',
    'manage_customize_blogs' => 'Manage Or Customize Blogs',
    'blog_message' => [
        'title_required' => 'The Title field is required.',
        'create_success' => 'New Blog created successfully.',
        'category_required' => 'The Category field is required.',
        'content_required' => 'The Content field is required.',
        'update_success' => 'Blog is updated successfully.',
        'update_error' => 'Failed to update the Blog.',
        'delete_success' => 'Blog is deleted successfully.',
    ],
    'page_message' => [
        'update_success' => 'Page is updated successfully.',
        'update_error' => 'Failed to update the page.',
    ],
    'enable_disable_features' => 'Enable/Disable Features',
    'post_settings' => 'Post Settings',
    'social_login_settings' => 'Social Login Settings',
    'add_new_game' => 'Add New Game',
    'add_new_movies' => 'Add New Movies',
    'fake_users' => 'Fake Users',
    'manage_auto_join_group' => 'Manage Auto Join Group',
    'auto_like_pages' => 'Auto Like Pages',
    'auto_friends' => 'Auto Friends',
    'auto_pages_updated' => 'Auto pages are updated',
    'fake_users_created' => 'Fake Users are created',
    'fake_users_deleted' => 'Fake users are deleted',
    'users_not_logged_in_one_week' => 'Users not logged in more than one week deleted successfully',
    'users_not_logged_in_one_month' => 'Users not logged in more than one month deleted successfully',
    'users_not_logged_in_one_year' => 'Users not logged in more than one year deleted successfully',
    'posts_not_logged_in_one_week' => 'Posts not logged in more than one week deleted successfully',
    'posts_not_logged_in_one_month' => 'Posts not logged in more than one month deleted successfully',
    'posts_not_logged_in_one_year' => 'Posts not logged in more than one year deleted successfully',
    'invalid_choice' => 'Invalid Choice',
    'header_label'      => 'Header Ad',
    'sidebar_label'     => 'Sidebar Ad',
    'first_post_label'  => 'First Post Ad',
    'second_post_label' => 'Second Post Ad',
    'third_post_label'  => 'Third Post Ad',
    'movie_name' => 'Movie Name',
    'genre' => 'Genre',
    'movie_thumbnail' => 'Movie Thumbnail',
    'video' => 'Video',
    'release_year' => 'Release Year',
    'rating' => 'Rating',
    'source' => 'Source',
    'validation_movie_name_required' => 'Please enter the movie name',
    'validation_genre_required' => 'Please enter the genre',
    'validation_stars_required' => 'Please enter the stars',
    'validation_producer_required' => 'Please enter the producer',
    'validation_duration_required' => 'Please enter the duration',
    'validation_description_required' => 'Please enter the description',
    'validation_description_minlength' => 'Description must be at least 6 characters long',
    'manage_advertisement' => 'Manage Advertisement',
    'manage_customize_ads' => 'Manage or Customize Ads',
    "job_is_updated"=>"Job is Updated",
    "job_is_deleted"=> "Job is Deleted",
    'first_name' => 'First Name',
    'last_name' => 'Last Name',
    'avatar' => 'Avatar',
    'male' => 'Male',
    'female' => 'Female',
    'date_of_birth' => 'Date Of Birth',
    'working' => 'Working',
    'update_user' => 'Update User',
    'user_update_success'=>'User updated successfully',
    'first_name_required' => 'Please enter the first name.',
    'last_name_required' => 'Please enter the last name.',
    'address_required' => 'Please enter the address.',
    'phone_required' => 'Please enter the phone number.',
    'working_required' => 'Please enter the working status.',
    'user_update_failed' => 'Failed to update user.',
    'user_delete_success' => 'User deleted successfully.',
    'user_delete_failed' => 'Failed to delete user.',
    'edit_package' => 'Edit Package',
    'role_assign_success' => 'New role assigned successfully',
    'role_assign_failure' => 'Failed to assign role to User',
    'sr' => 'Sr',
    'game_title' => 'Game Title',
    'game_description' => 'Game Description',
    'login_page_title'=>'Login Page Title',
    'validation_name_required' => 'Please enter the game title.',
    'validation_image_required' => 'Please select an image.',
    'validation_url_required' => 'Please enter the URL.',
    'validation_price_required' => 'Gift price is required.',
    'validation_name_minlength' => 'Name must be at least 3 characters',
    'validation_name_maxlength' => 'Name cannot exceed 255 characters',
    'validation_link_required' => 'Please enter the link',
    'new_password' => 'New Password',
    'confirm_password' => 'Confirm Password',
    'password_placeholder' => 'New Password',
    'confirm_password_placeholder' => 'Confirm Password',
    'change_password_btn' => 'Change Password',
    'password_mismatch' => 'Password does not match with confirm password',
    'password_required' => 'Please enter a password.',
    'password_minlength' => 'Password must be at least 6 characters long.',
    'confirm_password_required' => 'Please confirm your password.',
    'confirm_password_equalto' => 'Passwords do not match.',
     "bloodrequest_deleted_success" => "Blood request deleted successfully.",
    "package_created_success" => "Package is created.",
    "package_creation_failed" => "Failed to create the package.",
    "package_updated_success" => "Package is updated.",
    "package_update_failed" => "Failed to update the package.",
    "package_deleted_success" => "Package is deleted.",
    "package_deletion_failed" => "Failed to delete the package.",
    "gift_created_success" => "Gift is created.",
    "gift_creation_failed" => "Failed to create the Gift.",
    "movie_created_success" => "Movie created successfully",
    "movie_updated_success" => "Movie updated successfully.",
    "movie_deleted_success" => "Movie deleted successfully.",
    "space_system"=>'Space Section',
    'space_system_desc'=>'Enable or disable the space section on the user side in both the web and app.',
    'current_password' => 'Current Password',
    'enter_old_password' => 'Enter your old password',
    'enter_new_password' => 'Enter your new password',
    'confirm_new_password' => 'Confirm your new password',
    'update_password' => 'Update Password',
    'success_title' => 'Success',
    'error_title' => 'Error',
    'update_password_error' => 'Failed to update the password',
    'old_password_required' => 'The old password is required',
    'new_password_required' => 'The new password is required',
    'app_links' => 'App Links',
    'playstore_app_link' => 'Play Store App Link',
    'appstore_app_link' => 'App Store App Link',
    'playstore_app_link_desc' => 'Enter the link to your app on the Google Play Store.',
    'appstore_app_link_desc' => 'Enter the link to your app on the Apple App Store.',
    'edit_profile'=>'Edit Profile',
    'manage_admins'=>'Manage Admins',
    'add_new_admin' => 'Add New Admin',
    'email_required' => 'Email is required',
    'email_invalid' => 'Invalid email format',
    'admin_create_success' => 'Admin created successfully',
    'admin_create_failed' => 'Failed to create admin',
    'add_admin' => 'Add Admin',
    'no_deposit_found' => 'No deposit found',
    'select_movie_genre'=>'Select Movie Genre',
    'imdb_link' => 'IMDB Link',
    'productcategories' => 'Product Categories',
    'manage_or_customize_product_categories' => 'Manage or Customize Product Categories',
    'categoryname_required' => 'The category name is required.',
    'category_created_success' => 'Category created successfully.',
    'category_creation_failed' => 'Failed to create the category.',
    'edit_category' => 'Edit Category',
    'category_updated_success' => 'Category updated successfully.',
    'category_update_failed' => 'Failed to update the category.',
    'category_deleted_success' => 'Category deleted successfully.',
    'category_deletion_failed' => 'Failed to delete the category.',
    'categoryname'=>'Category Name',
    'productscategories'=>'Product Categories',
    'add_new_category'=>'Add New Category',
    'enter_category_name'=>'Enter Categroy Name',
    'groupcategories' => 'Group Categories',
    'manage_or_customize_group_categories' => 'Manage or Customize Group Categories',
    'can_not_delete' => 'Cannot delete this category',
    'jobcategories' =>'Job Categories',
    'manage_or_customize_job_categories'=>'Manage or Customize Job Categories',
    'event_categories'=>'Event Categories',
    'manage_or_customize_event_categories'=>'Manage or Customize Event Categories',
    'agora_setup' =>'Agora Setup',
    'agora_app_id' =>'Agora App ID',
    'agora_app_secret' =>'Agora App Secret',
    'coc_system_desc' => 'Enable/Disable Cup of Coffee on user Side in Web & App.',
    'gj_system_desc' => 'Enable/Disable Great Job on user Side in Web & App.',
    'cup_of_coffee' =>'Cup of Coffee',
    'great_job' =>'Great Job',
    'defualt_currency'=>'Default Currency',
    'defualt_currency_description'=>"The 'Default Currency' is the primary currency used for all transactions and financial operations unless a user specifies a different one. ",
    'chat_ai' => 'Chat AI', 
    'chatai_description' => 'This feature enables AI-based chat functionality for improved user interaction.', 
    'applicants'=>'Applicants',
    'course_title' => 'Course Title',
    'course_applicants'=>'Course Applicants',
    'course_description'=>'Course Description',
    'all_courses'=>'All Courses',
    'course_is_deleted' => 'The course is deleted.',
    'courses' => 'Courses',
    'course' => 'Course',
    'course_categories' => 'Course Categories',
    'manage_or_customize_course_categories' => 'Manage or Customize Course Categories',
    'course_system' => 'Course System',
    'course_system_description' => 'This feature enables a course management system for organizing and delivering educational content.',
    'poke_system' => 'Poke System',
    'poke_system_description' => 'This feature allows users to send virtual pokes to interact with each other in a fun way.',
    'event_system' => 'Event System',
    'event_system_description' => 'This feature enables event management, allowing users to create and manage events easily.',
    'business_categories' => 'Business Categories',
    'manage_business_categories' => 'Manage Business Categories',
    'add_new_business_category' => 'Add New Business Category',
    'edit_business_category' => 'Edit Business Category',
    'business_category' => 'Business Category',
    'facilities' => 'Facilities',
    'add_new_facility' => 'Add New Facility',
    'edit_facility' => 'Edit Facility',
    'manage_facilities' => 'Manage Facilities',
    'facility_name_required' => 'Facility name is required.',
    'facility_created_success' => 'Facility created successfully.',
    'facility_creation_failed' => 'Failed to create facility.',
    'facility_updated_success' => 'Facility updated successfully.',
    'facility_update_failed' => 'Failed to update facility.',
    'facility_deleted_success' => 'Facility deleted successfully.',
    'facility_deletion_failed' => 'Failed to delete facility.',
    'cannot_delete_default_facility' => 'Cannot delete this default facility.',
    'facility_name' => 'Facility Name',
    'enter_facility_name' => 'Enter facility name',
    'add_new_tag' => 'Add New Tag',
    'edit_tag' => 'Edit Tag',
    'tag_name' => 'Tag Name',
    'manage_tags' => 'Manage Tags',
    'tag_name_required' => 'Tag name is required.',
    'tag_created_success' => 'Tag created successfully.',
    'tag_creation_failed' => 'Failed to create tag.',
    'tag_updated_success' => 'Tag updated successfully.',
    'tag_update_failed' => 'Failed to update tag.',
    'tag_deleted_success' => 'Tag deleted successfully.',
    'tag_deletion_failed' => 'Failed to delete tag.',
    'cannot_delete_default_tag' => 'Cannot delete this default tag.',
    'enter_tag_name' => 'Enter tag name',
    'google_map_key' => 'Google Map Key',
    'google_map_desc' => 'Enable/Disable google map on user Side in Web & App.',

];
