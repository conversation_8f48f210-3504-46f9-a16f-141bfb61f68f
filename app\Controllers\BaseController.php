<?php

namespace App\Controllers;

use CodeIgniter\Controller;
use CodeIgniter\HTTP\CLIRequest;
use CodeIgniter\HTTP\IncomingRequest;
use CodeIgniter\HTTP\RequestInterface;
use CodeIgniter\HTTP\ResponseInterface;
use Psr\Log\LoggerInterface;


/**
 * Class BaseController
 *
 * BaseController provides a convenient place for loading components
 * and performing functions that are needed by all your controllers.
 * Extend this class in any new controllers:
 *     class Home extends BaseController
 *
 * For security be sure to declare any new methods as protected or private.
 */
abstract class BaseController extends Controller
{
    /**
     * Instance of the main Request object.
     *
     * @var CLIRequest|IncomingRequest
     */
    protected $request;

    /**
     * An array of helpers to be loaded automatically upon
     * class instantiation. These helpers will be available
     * to all other controllers that extend BaseController.
     *
     * @var array
     */
    protected $helpers = [];
    protected $appConfig;
    protected $activeTheme;
    protected $activeThemePath = "";
    protected $data = [];

    public function __construct()
    {
        $this->data['user_data'] =  getCurrentUser();
    }


    /**
     * Constructor.
     */
    public function initController(RequestInterface $request, ResponseInterface $response, LoggerInterface $logger)
    {
        get_setting('developer_mode');
        // Do Not Edit This Line
        parent::initController($request, $response, $logger);

        // Load theme config
        //config('Themes');
        // Preload any models, libraries, etc, here.
        $this->appConfig = config('App');
        // E.g.: $this->session = \Config\Services::session();
        $this->activeThemePath = 'themes/' . ACTIVE_THEME . '/views/';
    }

    protected function renderThemeView($view, $data = [], $return = false)
    {
        $themeViewPath = 'themes/' . ACTIVE_THEME . '/views/' . $view . '.php';

        return view($themeViewPath, $data, ['saveData' => true, 'viewPath' => 'themes'], $return);
    }
}
