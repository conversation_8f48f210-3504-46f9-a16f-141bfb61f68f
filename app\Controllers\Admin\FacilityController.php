<?php

namespace App\Controllers\Admin;

use App\Controllers\BaseController;
use App\Models\Facility;

class FacilityController extends BaseController
{
    public function index()
    {
        $this->data['page_title'] = lang('Admin.facilities');
        $facilityModel = new Facility();

        $this->data['page'] = $this->request->getVar('page') ?? 1;
        $perPage = 20;
        $this->data['facilities'] = $facilityModel->paginate($perPage);
        $this->data['pager'] = $facilityModel->pager;
        $this->data['perPage'] = $perPage;
        $this->data['breadcrumbs'][] = ['name' => lang('Admin.manage_facilities'), 'url' => ''];

        return view('admin/pages/facilities/index', $this->data);
    }
    public function store()
    {
        $validationRules = [
            'name' => [
                'label' => 'Facility Name',
                'rules' => 'required',
                'errors' => [
                    'required' => lang('Admin.facility_name_required'),
                ],
            ],
        ];
        if (!$this->validate($validationRules)) {
            $this->data['page_title'] = lang('Admin.add_new_facility');
            $this->data['js_files'] = ['assets/js/jquery.validate.min.js'];
            $this->data['breadcrumbs'][] = ['name' => lang('Admin.add_new_facility'), 'url' => ''];
            $this->data['validation'] = $this->validator->getErrors();
            return view('admin/pages/facilities/create', $this->data);
        }
        $data = [
            'name' => $this->request->getVar('name'),
        ];
        $facilityModel = new Facility();
        $res = $facilityModel->save($data);
        $session = \Config\Services::session();

        if ($res) {
            $session->setFlashdata('success', lang('Admin.facility_created_success'));
            return redirect('admin/facilities');
        } else {
            $session->setFlashdata('error', lang('Admin.facility_creation_failed'));
            return redirect('admin/facilities/create');
        }
    }
    public function edit($id)
    {
        $this->data['page_title'] = lang('Admin.edit_facility');
        $facilityModel = new Facility();
        $this->data['js_files'] = ['assets/js/jquery.validate.min.js'];
        $this->data['breadcrumbs'][] = ['name' => lang('Admin.edit_facility'), 'url' => ''];
        $this->data['facility'] = $facilityModel->find($id);
        return view('admin/pages/facilities/edit', $this->data);
    }

    public function update($id)
    {
        $validationRules = [
            'name' => [
                'rules' => 'required',
                'errors' => [
                    'required' => lang('Admin.facility_name_required'),
                ],
            ],
        ];
        $session = \Config\Services::session();
        $facilityModel = new Facility();

        if (!$this->validate($validationRules)) {
            $this->data['js_files'] = ['assets/js/jquery.validate.min.js'];
            $this->data['breadcrumbs'][] = ['name' => lang('Admin.edit_facility'), 'url' => ''];
            $this->data['facility'] = $facilityModel->find($id);
            $this->data['validation'] = $this->validator->getErrors();

            return view('admin/pages/facilities/edit', $this->data);
        }
        $data = ['name' => $this->request->getVar('name')];

        $res = $facilityModel->update($id, $data);
        if ($res) {
            $session->setFlashdata('success', lang('Admin.facility_updated_success'));
        } else {
            $session->setFlashdata('error', lang('Admin.facility_update_failed'));
        }
        return redirect('admin/facilities');
    }

    public function delete($id)
    {
        $session = \Config\Services::session();
        $facilityModel = new Facility();
        if ($id > 10) {
            $res = $facilityModel->delete($id);
            if ($res) {
                $session->setFlashdata('success', lang('Admin.facility_deleted_success'));
            } else {
                $session->setFlashdata('error', lang('Admin.facility_deletion_failed'));
            }
        } else {
            $session->setFlashdata('error', lang('Admin.cannot_delete_default_facility'));
        }
        return redirect('admin/facilities');
    }
}
