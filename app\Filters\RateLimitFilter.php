<?php

namespace App\Filters;

use CodeIgniter\Filters\FilterInterface;
use CodeIgniter\HTTP\RequestInterface;
use CodeIgniter\HTTP\ResponseInterface;

class RateLimitFilter implements FilterInterface
{
    /**
     * Do whatever processing this filter needs to do.
     * By default it should not return anything during
     * normal execution. However, when an abnormal state
     * is found, it should return an instance of
     * CodeIgniter\HTTP\Response. If it does, script
     * execution will end and that Response will be
     * sent back to the client, allowing for error pages,
     * redirects, etc.
     *
     * @param RequestInterface $request
     * @param array|null       $arguments
     *
     * @return mixed
     */
    public function before(RequestInterface $request, $arguments = null)
    {
        $throttler = service('throttler');

        // Allow 5 attempts per minute (adjust the limit as needed)
        if ($throttler->check(md5($request->getIPAddress()), 5, MINUTE) === false) {
       $response = [
                'status'   => 'error',
                'error'    => 429,
                'messages' => 'Too many requests. Please try again later.'
            ];

            // Return the response as JSON with a 429 status code
            return service('response')
                ->setStatusCode(429) // HTTP status code 429 for Too Many Requests
                ->setHeader('Content-Type', 'application/json') // Set content type to JSON
                ->setBody(json_encode($response)); // Encode the response as JSON
        
        }
    }

    /**
     * Allows After filters to inspect and modify the response
     * object as needed. This method does not allow any way
     * to stop execution of other after filters, short of
     * throwing an Exception or Error.
     *
     * @param RequestInterface  $request
     * @param ResponseInterface $response
     * @param array|null        $arguments
     *
     * @return mixed
     */
    public function after(RequestInterface $request, ResponseInterface $response, $arguments = null)
    {
        //
    }
}
