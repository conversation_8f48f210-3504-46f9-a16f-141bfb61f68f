<?php

namespace App\Controllers;

use App\Models\Course;
use App\Models\CourseApplicant;
use App\Models\CourseCategory;
use App\Models\UserModel;
use CodeIgniter\API\ResponseTrait;
use App\Controllers\BaseController;
use App\Models\TransactionModel;

class CourseController extends BaseController
{
    use ResponseTrait;
    public function addCourse()
    {
          
        $rules = [
            'title' => [
                'label' => 'Request Action',
                'rules' => 'required',
                'errors' => [
                    'required' => lang('Api.title_required'),
                ],
            ],
            'address' => [
                'label' => 'Request Action',
                'rules' => 'required',
                'errors' => [
                    'required' => lang('Api.address_required'),
                ],
            ],
            'start_date' => [
                'label' => 'Request Action',
                'rules' => 'required',
                'errors' => [
                    'required' => lang('Api.start_date_required'),
                ],
            ],
            'end_date' => [
                'label' => 'Request Action',
                'rules' => 'required',
                'errors' => [
                    'required' => lang('Api.end_date_required'),
                ],
            ],
            'category' => [
                'label' => 'Request Action',
                'rules' => 'required',
                'errors' => [
                    'required' => lang('Api.category_required'),
                ],
            ],
            'country' => [
                'label' => 'Request Action',
                'rules' => 'required',
                'errors' => [
                    'required' => lang('Api.country_required'),
                ],
            ],
            'level' => [
                'label' => 'Request Action',
                'rules' => 'required',
                'errors' => [
                    'required' => lang('Api.level_required'),
                ],
            ],
            // 'is_paid' => [
            //     'label' => 'Request Action',
            //     'rules' => 'required',
            //     'errors' => [
            //         'required' => lang('Api.is_paid_required'),
            //     ],
            // ],
            // 'amount' => [
            //     'label' => 'Request Action',
            //     'rules' => 'required',
            //     'errors' => [
            //         'required' => lang('Api.amount_required'),
            //     ],
            // ],
            'language' => [
                'label' => 'Request Action',
                'rules' => 'required',
                'errors' => [
                    'required' => lang('Api.language_required'),
                ],
            ],

        ];

        if(!$this->validate($rules))
        {
            return $this->respond([
                'code' => '400',
                'message' => lang('Api.validation_error'),
                'data' => $this->validator->getErrors()
            ], 200);
  
        }
        $user = getCurrentUser();
        $data = [
            'created_by'=>$user['id'],
            'title'=>$this->request->getVar('title'),
            'address'=>$this->request->getVar('address'),
            'start_date'=>$this->request->getVar('start_date'),
            'end_date'=>$this->request->getVar('end_date'),
            'category_id'=>$this->request->getVar('category'),
            'country'=>$this->request->getVar('country'),
            'level'=>$this->request->getVar('level'),
            'language'=>$this->request->getVar('language'),
            'description'=>$this->request->getVar('description'),
            'is_paid'=>!empty($this->request->getVar('is_paid'))?1:0,
            'amount'=>$this->request->getVar('amount')??0,

        ];  
        $cover = $this->request->getFile('cover');
        if (!empty($cover) && $cover->isValid()) {
            $cover = storeMedia($cover, 'cover');
            $data['cover'] = $cover;
        }
        $courseModel = New Course();
        $courseModel->save($data);
        return $this->respond([
            'code' => '200',
            'message' => lang('Api.create_course_success'),
        ], 200);
    }
    public function allCourses()
    {
        $courseModel = New Course();
        $user  = getCurrentUser();
        $limit = $this->request->getVar('limit')??10;
        $offset = $this->request->getVar('offset')??0;
        $category_id = $this->request->getVar('category_id')??'';
        
        $user_id = $this->request->getVar('user_id') ?? 0; // Default to 0 if 'user_id' is not passed
        $search = $this->request->getVar('search_string') ?? '';
        // Apply filter conditionally
        $query = $courseModel->orderBy('id', 'desc');
        if (!empty($search)) {
            $query->like('title', $search); // Change 'course_name' to the field you want to search
        }
        if (!empty($category_id)) {
            $query->like('category_id', $category_id); // Change 'course_name' to the field you want to search
        }
        if (!empty($search)) {
            $query->like('title', $search); // Change 'course_name' to the field you want to search
        }
        // If user_id is greater than 0, add where condition for that user
        if ($user_id > 0) {
            $query->where('created_by', $user_id);
        } else {
            $query->where('created_by !=', $user['id']);
        }
        
        // Fetch courses with the limit and offset
        $courses = $query->findAll($limit, $offset);
        $allcourse = [];
        if(count($courses)>0)
        {
            foreach($courses as $course)
            {
                $allcourse[] = $courseModel->courseDetail($course,$user['id']);
            }
            return $this->respond([
                'code' => '200',
                'message' => lang('Api.course_fetch_success'),
                'data'=>$allcourse
            ], 200);
        }
        return $this->respond([
            'code' => '200',
            'message' => lang('Api.course_not_found'),
        ], 200);
    }
    public function allWebCourses()
    {
        $category = $this->request->getVar('category');
        $user_id = getCurrentUser()['id'];
        $model  = New Course();
        $pager  = service('pager');
        $perPage = 10;
        $search = $this->request->getVar('search')??'';
        $page = (int) $this->request->getVar('page')??1;
        $category = $this->request->getVar('category');
        
        $total = $model->where('created_by !=', $user_id)->countAllResults();
        $query = $model->where('created_by !=', $user_id);
        if(!empty($category)){
            $query = $model->where('category_id', $category);
        }
        if (!empty($search)) {
            $query->like('title', $search); // Change 'course_name' to the field you want to search
        }
        $courseCategoryModel = New CourseCategory();
        $this->data['categories'] = $courseCategoryModel->findAll();
        
        $courses = $query->findAll();
        $levelArray = [
            1=>'Beginner',
            2=>'Intermediate',
            3=>'Advanced'
        ];
        $courseApplicantModel  = New CourseApplicant();
        if (!empty($courses)) {
            foreach ($courses as &$course) {
                $userModel  =  New UserModel();
                $course['cover'] = getMedia($course['cover']);
                $course['is_applied'] = $courseApplicantModel->checkAlreadyApplied($user_id,$course['id']);
                $course['is_paid'] = ($course['is_paid']==0)?'Free':'Paid';
                $course['level'] = $levelArray[$course['level']];
                $course['user'] = $userModel->getUserShortInfo($course['created_by']);
            }
        }
        // Calculate total pages for pagination

        $this->data['courses'] = $courses;
        $this->data['category'] = $category;
        $this->data['search'] = $search;
        
        echo load_view('pages/courses/all', $this->data);

    }
    public function myCourses()
    {
        $user_id = getCurrentUser()['id'];
        $search  = $this->request->getVar('search')??'';
        $model  = New Course();
        $pager  = service('pager');
        $perPage = 10;
        $page = (int) $this->request->getVar('page')??1;
        
        $total = $model->where('created_by', $user_id)->countAllResults();
        $query = $model->where('created_by', $user_id);
        if (!empty($search)) {
            $query->like('title', $search); // Change 'course_name' to the field you want to search
        }
        $courseCategoryModel = New CourseCategory();
        $this->data['categories'] = $courseCategoryModel->findAll();
        
        $courses = $query->findAll();
        $levelArray = [
            1=>'Beginner',
            2=>'Intermediate',
            3=>'Advanced'
        ];
        if (!empty($courses)) {
            foreach ($courses as &$course) {
               
                $userModel  =  New UserModel();
                $course['cover'] = getMedia($course['cover']);
                $course['is_applied'] = 0;
                $course['is_paid'] = ($course['is_paid']==0)?'Free':'Paid';
                $course['level'] = $levelArray[$course['level']];
                $course['user'] = $userModel->getUserShortInfo($course['created_by']);

            }
        }
        // Calculate total pages for pagination

        $this->data['courses'] = $courses;
        $this->data['search'] = $search;
     
        echo load_view('pages/courses/my-courses', $this->data);

    }
    public function addCourses()
    {
        $courseCategoriesModel = New CourseCategory();
        $this->data['categories'] = $courseCategoriesModel->findAll(); 
        echo load_view('pages/courses/create-course', $this->data);

    }
    
    public function updateCourse()
    {

        $rules = [
            'course_id' => [
                'rules' => 'required',
                'errors' => [
                    'required' => lang('Api.course_id_required'),
                ],
            ],
        ];
        if ($this->validate($rules)) {
            $course_id = $this->request->getVar('course_id');
            $courseModel  = New Course();
            $course =  $courseModel->find($course_id);
            if(empty($course))
            {
                return $this->respond([
                    'code' => '400',
                    'message' => lang('Api.course_not_found'),
                ], 200);
            }
            $user = getCurrentUser();
            if($course['created_by']!=$user['id'])
            {
                return $this->respond([
                    'code' => '400',
                    'message' => lang('Api.unautthenticated'),
                ], 200);
            }
            $update_data = [];

            // Loop through all input values and add them to the update array
            foreach ($this->request->getPost() as $key => $value) {
                // Exclude the 'page_id' from the update array
                if ($key != 'course_id') {
                    $update_data[$key] = trim($value);
                }
            }

            if(!empty($update_data['is_paid']) &&( $update_data['is_paid'] == "on" ||$update_data['is_paid']==1 )){
                $update_data['amount'] = $this->request->getVar('amount');
                $update_data['is_paid'] = 1;
                
            }else{
                $update_data['amount'] = 0;
                $update_data['is_paid'] = 0;
            }


            if(isset( $update_data['is_paid'] ) && $update_data['is_paid'] == 1 && $update_data['amount'] == 0){
                return $this->respond([
                    'code' => '400',
                    'message' => lang('Api.course_price_not_zero'),
                ], 200);
            }
            $cover = $this->request->getFile('cover');
            if (!empty($cover) && $cover->isValid() && !$cover->hasMoved() && $cover->getSize() > 0) {    
                $courseCover = storeMedia($cover, 'cover');
                $update_data['cover'] = $courseCover;
            }
            
            
            // Validate and update the specified fields
            if (!empty($update_data)) {
                $courseModel->update($course_id, $update_data);
                
            }

            return $this->respond([
                'code' => '200',
                'message' => lang('Api.course_updated_successfully'),
            ], 200);
        } 
        else
        {
            $validationErrors = $this->validator->getErrors();
            return $this->respond([
                'code' => '400',
                'message' => lang('Api.validation_error'),
                'data' => $validationErrors
            ], 200);
        }    
    }
    public function deleteCourse()
    {

        $rules = [
            'course_id' => [
                'rules' => 'required',
                'errors' => [
                    'required' => lang('Api.course_id_required'),
                ],
            ],
        ];
        if ($this->validate($rules)) {
            $course_id = $this->request->getVar('course_id');
            
            $courseModel  = New Course();
            $course =  $courseModel->find($course_id);
            if(empty($course))
            {
                return $this->respond([
                    'code' => '400',
                    'message' => lang('Api.course_not_found'),
                    
                ], 200);
            }
            $user = getCurrentUser();
            if($course['created_by']!=$user['id'])
            {
                return $this->respond([
                    'code' => '400',
                    'message' => lang('Api.unautthenticated'),
                ], 200);
            }
            $courseModel->delete($course_id);
            return $this->respond([
                'code' => '200',
                'message' => lang('Api.course_deleted_successfully'),
            ], 200);
        } 
        else
        {
            $validationErrors = $this->validator->getErrors();
            return $this->respond([
                'code' => '400',
                'message' => lang('Api.validation_error'),
                'data' => $validationErrors
            ], 200);
        }    
    }
    public function editCourse($id)
    {
        $courseCategoriesModel = New CourseCategory();
        $courseModel = New Course();
        $course = $courseModel->find($id);
        if(empty($course))
        {
            throw \CodeIgniter\Exceptions\PageNotFoundException::forPageNotFound();
        }
        elseif($course['created_by']!=getCurrentUser()['id'])
        {
            throw \CodeIgniter\Exceptions\PageNotFoundException::forPageNotFound();
        }
        $this->data['course'] = $course;
        $this->data['categories'] = $courseCategoriesModel->findAll(); 
        echo load_view('pages/courses/edit-course', $this->data);
    }
    public function ApplyInCourse()
    {
        $rules = [
            'course_id' => [
                'rules' => 'required',
                'errors' => [
                    'required' => lang('Api.course_id_required'),
                ],
            ],
        ];
        if ($this->validate($rules)) {
            $course_id = $this->request->getVar('course_id');
            
            $courseModel  = New Course();
            $course =  $courseModel->find($course_id);
            if(empty($course))
            {
                return $this->respond([
                    'code' => '400',
                    'message' => lang('Api.course_not_found'),
                    
                ], 200);
            }
            $user = getCurrentUser();
            if($course['created_by']==$user['id'])
            {
                return $this->respond([
                    'code' => '400',
                    'message' => lang('Api.can_not_apply_your_own_course'),
                ], 200);
            }
            $courseAppliedModel = New CourseApplicant();
            if($courseAppliedModel->checkAlreadyApplied($user['id'],$course_id)==1)
            {
                return $this->respond([
                    'code' => '400',
                    'message' => lang('Api.already_applied'),
                ], 200);
                
            }
            if($course['is_paid']==1)
            {
                if(getuserwallet($user['id'])<$course['amount'])
                {
                    return $this->respond([
                        'code' => '400',
                        'message' => lang('Api.insufficent_balance'),
                    ], 200); 
                }
                $transactionModel = New TransactionModel();
                $debitTransaction = [
                    'user_id' => $user['id'],
                    'flag' => 'D',
                    'action_type' => 16,
                    'amount' => $course['amount'],
                ];
                $transactionModel->save($debitTransaction);
                $creditTransaction = [
                    'user_id' => $course['created_by'],
                    'flag' => 'C',
                    'action_type' => 16,
                    'amount' => $course['amount'],
                ];
                $transactionModel->save($creditTransaction);
              
                $amount_status  = 1;
            }
            $courseData = [
                'course_id'=>$course['id'],
                'user_id'=>$user['id'],
                'payment_status'=>$amount_status??0
            ];
            $courseAppliedModel->save($courseData);
            return $this->respond([
                'code' => '200',
                'message' => lang('Api.applied_success'),
            ], 200);
        }
        else
        {
            $validationErrors = $this->validator->getErrors();
            return $this->respond([
                'code' => '400',
                'message' => lang('Api.validation_error'),
                'data' => $validationErrors
            ], 200);
        }
    } 
    public function courseDetails($id)
    {
        $CourseApplicationModel = New CourseApplicant();
        $courseCategoryModel = New CourseCategory();
        $this->data['categories'] = $courseCategoryModel->findAll();
        $this->data['user_data'] = getCurrentUser(); 
        $user_id = getCurrentUser()['id'];
        $courseModel  = New Course;
        $course =  $courseModel->find($id);
        $this->data['category'] = $courseCategoryModel->find($course['id'])??'';
        $course['is_applied'] = $CourseApplicationModel->checkAlreadyApplied($user_id, $course['id']);
        $this->data['course'] = $course;
        $this->data['course']['user_id'] = $course['created_by'];
        $this->data['applicants'] = $CourseApplicationModel->getApplicantDetail($id);
        echo load_view('pages/courses/course-detail', $this->data); 
    } 
    public function apiCourseDetails()
    {
        $rules = [
            'course_id' => [
                'rules' => 'required',
                'errors' => [
                    'required' => lang('Api.course_id_required'),
                ],
            ],
        ];
        if (!$this->validate($rules)) {
            $validationErrors = $this->validator->getErrors();
            return $this->respond([
                'code' => '400',
                'message' => lang('Api.validation_error'),
                'data' => $validationErrors
            ], 200);
        }
        $courseModel  = New Course();
        $course_id  = $this->request->getVar('course_id');
        $course =  $courseModel->find($course_id);
        if(empty($course))
        {
            return $this->respond([
                'code' => '400',
                'message' => lang('Api.course_not_found'),
                
            ], 200);
        }
        $course['cover'] = getMedia($course['cover']);
        $course['is_paid'] = ($course['is_paid']==0)?'Free':'Paid';
        $courseCategoryModel = New CourseCategory();
        $course['category'] = $courseCategoryModel->find($course['id'])?$courseCategoryModel->find($course['id'])['name']:'';
        $CourseApplicationModel = New CourseApplicant();
        $loggedInUserId = getCurrentUser()['id'];
        $applicants = $CourseApplicationModel->getApplicantDetail($course_id);
        $course['applicant_count'] = count($applicants) ;
        $course['is_applied'] = $CourseApplicationModel->checkAlreadyApplied($loggedInUserId, $course['id']);
        $course['applicants'] = [];
        if($course['created_by']==$loggedInUserId)
        {
            if(count($applicants))
            {
                foreach($applicants as &$applicant)
                {
                   $applicant['avatar'] = getMedia($applicant['avatar']);
                }
                $course['applicants'] = $applicants;
            }
        }
        return $this->respond([
            'code' => '200',
            'message' => lang('Api.course_fetch_success'),
            'data'=>$course
        ], 200);
    }
}
