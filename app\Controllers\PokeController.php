<?php

namespace App\Controllers;

use App\Models\Friend;
use App\Models\PokeModel;
use App\Models\UserModel;
use CodeIgniter\API\ResponseTrait;



class PokeController extends BaseController
{
    protected $pokeModel;
    protected $user_id;
    use ResponseTrait;
    public function __construct()
    {
        $this->user_id = getCurrentUser()['id'];
        $this->pokeModel = new PokeModel();
    }

    public function index(){
        $user_data = getCurrentUser();
        $this->data['user_data'] = $user_data;

        $loggedInUserId = getCurrentUser()['id'];
        $pokeModel = new PokeModel();
    
        // Correctly group the logical condition
        $pokeModel->where('to_user', $loggedInUserId)->where('is_back', 0);
        
        $pokes = $pokeModel->orderBy('id','desc')->findAll();
    
        $pokeData = [];
        if (!empty($pokes)) {
            foreach ($pokes as $key => $poke) {
                $userModel = new UserModel();
                $userdata = $userModel->getUserShortInfo($poke['from_user']);    
                if(!empty($userdata)){
                    $userdata['is_back'] = $poke['is_back'];
                    $pokeData[] = $userdata;
                }
            }

        }
        $this->data['js_files'] = ['js/posts.js',
									'js/post_plugins.js',
									'vendor/imagepopup/js/lightbox.min.js',
								];

		$this->data['css_files'] = ['css/posts.css',
									'css/posts_plugins.css',
									'vendor/imagepopup/css/lightbox.min.css'
									];
        $this->data['pokes'] = $pokeData;
        echo load_view('pages/poke/index', $this->data);
    }


}
