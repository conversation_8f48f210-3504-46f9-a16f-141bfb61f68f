<?php
 namespace App\Controllers;

use App\Models\Page;
use App\Models\ChatModel;
use App\Models\NotificationModel;
 use App\Models\UserModel;
 use CodeIgniter\API\ResponseTrait;
 use App\Controllers\BaseController;

class Home extends BaseController
{
	use ResponseTrait;

	 public function __construct()
    {
		parent::__construct();
		
    }

	public function index(){
		$user = getCurrentUser();
        
        if(!is_profile_completed($user))
        {
             return redirect('start');
        }
		$this->data['js_files'] = ['js/posts.js',
									'js/post_plugins.js',
									'vendor/imagepopup/js/lightbox.min.js',
								];

		$this->data['css_files'] = ['css/posts.css',
									'css/posts_plugins.css',
									'vendor/imagepopup/css/lightbox.min.css'
									];
		
		if(get_setting('directory_landing_page')=='home') {
			echo load_view('newsfeed',$this->data);
		} else if(empty(getCurrentUser())){
			return redirect('login');
		}
		// echo load_view('newsfeed',$this->data);
	}

	public function setLanguage($language)
	{
		// Ensure the language exists in the available languages
		$availableLanguages = get_available_languages();
		log_message('debug', 'HTTP_REFERER: ' . ($_SERVER['HTTP_REFERER'] ?? 'No Referer'));
		// Check if the selected language is valid
		if (in_array($language, array_keys($availableLanguages))) {
			// Set the language in session
			session()->set('lang', $language);
	
			// Redirect back only if a referer is available
			if (isset($_SERVER['HTTP_REFERER'])) {
				return redirect()->back()->with('message', 'Language updated successfully');
			}
	
			// Default to the home page if no referer is set
			return redirect()->to('/')->with('message', 'Language updated successfully');
		}
	
		// If the language is not valid, return with an error
		return redirect()->to('/')->with('error', 'Invalid language selected');
	}
	

	public function savedpost()
    {
        $this->data['js_files'] = ['js/posts.js',
									'js/post_plugins.js',
									'vendor/imagepopup/js/lightbox.min.js',
								];

		$this->data['css_files'] = ['css/posts.css',
									'css/posts_plugins.css',
									'vendor/imagepopup/css/lightbox.min.css'
									];
        echo load_view('pages/saved_posts', $this->data);   

    }

	public function updates()
	{
		$loggedInUser = getCurrentUser();
		$loggedInUserId = $loggedInUser['id'];
		$usertimetimezone  = $loggedInUser['timezone'];
		$to_id = $this->request->getVar('user_id');
		$currenttimezone = $this->request->getVar('timezone');
		$responseData = [];
		if($currenttimezone!=$usertimetimezone)
		{
				$userModel  = New UserModel();
				$userModel->where('id',$loggedInUserId)->set('timezone',$currenttimezone)->update();
				$_SESSION['timezone'] = $currenttimezone;
		}
		$notificationModel = New NotificationModel();
		$responseData['unseen_notifications'] = $notificationModel->where('to_user_id',$loggedInUserId)->where('seen',0)->countAllResults();
		$responseData['unseen_messages'] = [];
		if($to_id!=null && !empty($to_id))
		{
			$responseData['unseen_messages'] = $this->getMessages($loggedInUserId, $to_id, 1000, 0, $loggedInUserId);
		}		
		return $this->respond([
			'code' => '200',
			'message' => 'Success',
			// 'timezone' => $currenttimezone,
			'data' => $responseData
		], 200);
	}
	public function getMessages($from_id, $to_id, $limit, $offset, $currentUserId)
    {

		$messageModel = New ChatModel();
        $messageModel->where('from_id', $to_id)
            ->where('to_id', $currentUserId)
            ->where('is_seen',0)
            ->set('is_seen', 1)
        ->update();

        $messages = $messageModel
            ->where('deleted_at', null)
            ->groupStart()
                ->where("from_id", $from_id)
                ->where("to_id", $to_id)
            ->groupEnd()
            ->orGroupStart()
                ->where("from_id", $to_id)
                ->where("to_id", $from_id)
            ->groupEnd()
           
            ->findAll($limit, $offset);

        if (!empty($messages)) {
            foreach ($messages as &$message) {
                $message['media'] = !empty($message['media']) ? getMedia($message['media']) : '';
                $message['thumbnail'] = !empty($message['thumbnail']) ? getMedia($message['thumbnail']) : '';
                // Determine the other user's ID
                $otherUserId = ($message['from_id'] == $currentUserId) ? $message['to_id'] : $message['from_id'];

                $message['user'] = $this->getUserData($otherUserId);
                $message['human_time'] = HumanTime($message['created_at']);
				$message['local_time'] = getRealTime($message['created_at']);
				

            }
        }
        return $messages;
    }
	public function getUserData($userId)
    {
		$db = \Config\Database::connect();
        $userdata = $db->table('users')
                    ->select(["avatar", "id", "username", "CONCAT(first_name, ' ', last_name) as name"]) // Note the space between first_name and last_name
                    ->where('id', $userId)
                    ->get()
                    ->getFirstRow();

        if (!empty($userdata)) {
            $userdata = (array) $userdata;
            $userdata['avatar'] = getMedia($userdata['avatar'], 'avatar');
        }

        return $userdata;
    }

	public function videoTimeline()
	{
		$this->data['js_files'] = ['js/posts.js',
									'js/post_plugins.js',
									'vendor/imagepopup/js/lightbox.min.js',
								];

		$this->data['css_files'] = ['css/posts.css',
									'css/posts_plugins.css',
									'vendor/imagepopup/css/lightbox.min.css'
									];
		
		
		echo load_view('videoTimeline',$this->data);
		

		//echo load_view('newsfeed',$this->data);
	}

	public function savedPosts()
	{
		$this->data['js_files'] = ['js/posts.js',
									'js/post_plugins.js',
									'vendor/imagepopup/js/lightbox.min.js',
								];

		$this->data['css_files'] = ['css/posts.css',
									'css/posts_plugins.css',
									'vendor/imagepopup/css/lightbox.min.css'
									];
		
		
		echo load_view('videoTimeline',$this->data);

		//echo load_view('newsfeed',$this->data);
	}
	
	// complete profile
	public function start()
	{
		$this->data['css_files'] = ['css/welcome.css'];
		
		$this->data['is_full_layout'] =1;
		$userModel = New UserModel;
		$user = getCurrentUser();
		$this->data['userdata'] = $userModel->select(['phone','twitter','instagram','facebook','date_of_birth','youtube','linkedin','phone','first_name','last_name','cover','avatar','gender'])->where('id',$user['id'])->first();
		echo load_view('pages/welcome/start',$this->data);
	}


	public function myGroups()
	{
		echo load_view('my-groups',$this->data);
	}


	public function login()
	{
		
		echo load_view('login');
		
	}

	public function js_language()
	{
		
		header('Content-Type: application/javascript');
		echo load_view('lang');
		
	}
	



	public function access_denied(){
		return view('errors/html/access_denied'); 
	}
	// public function activateAccount()
	// {
	// 	return view('emails/activateaccount'); 
	// }

	//--------------------------------------------------------------------

}
