<?php

/**
 * --------------------------------------------------------------------
 * CODEIGNITER 4 - SimpleAuth
 * --------------------------------------------------------------------
 *
 * This content is released under the MIT License (MIT)
 *
 * @package    SimpleAuth
 * <AUTHOR> - <PERSON>kelding 
 * @license    https://opensource.org/licenses/MIT	MIT License
 * @link       https://github.com/GeekLabsUK/SimpleAuth
 * @since      Version 1.0
 * 
 */

return [

    'resetSuccess'              => 'Password Reset Successfully. Please Login',
    'activateSuccess'           => 'Account activated successfully. Please Login', 
    'resetSent'                 => 'Please check your email for the link to reset your password',
    'errorOccured'              => 'An error occured',
    'linkExpired'               => 'Reset link has expired',
    'noAuth'                    => 'Could not authorise please try again',
    'notActivated'              => 'Account not activated.',
    'accountCreated'            => 'account created. please check your email to activate your account',
    'accountCreatedNoAuth'      => 'account created. please Login',
    'acitvationEmailReSent'     => 'Activation email re-sent. please check your email to activate your account',
    'noUser'                    => 'No user found with this Email address',
    'successUpdate'             => 'Profile succesfully updated',
    'passwordAuthorised'        => 'Password reset authorised.',
    'login_failed'              => 'Invalid username or password',
    'mailsenterror'              => 'Email not sent due to some reason',
    'register_success'           =>    "Registered successfully. Please log in your account to continue",
    
    'first_name_required' => 'Please enter your first name',
    'last_name_required' => 'Please enter your last name',
    'email_required' => 'Please enter your email address',
    'email_invalid' => 'Please enter a valid email address',
    'password_required' => 'Please provide a password',
    'password_minlength' => 'Your password must be at least 6 characters long',
    'password_confirm_required' => 'Please confirm your password',
    'password_confirm_equalTo' => 'Please enter the same password as above',
    'gender_required' => 'Please select your gender',
    'date_of_birth_required' => 'Please enter your date of birth',
    'date_of_birth_invalid' => 'Please enter a valid date',
    'reset_password' => 'Reset Password',
    'reset_password_instruction' => 'Enter your email address to reset your password',
    'email_address' => 'Email Address',
    'reset_button' => 'Reset',
    'success_message' => 'Password reset instructions sent successfully!',
    'error_message' => 'An error occurred. Please try again.',
    'create_account' => 'Create an Account',
    'dont_have_account' => 'Dont have an account?',
    'date_of_birth_datebeforefifteenyear' => 'To use application , you must be 15 years old or above.',
    'write_your_password' => 'Write your password...',
    'easy_peasy' => 'Easy peasy!',
    'simple_one' => 'That is a simple one',
    'better_one' => 'That is better',
    'password_rocks' => 'Yeah! that password rocks ;)',

];